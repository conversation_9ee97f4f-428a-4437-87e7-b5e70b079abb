/**
 * Shared Permission and Policy Constants
 * Used across both API and Web layers for consistent authorization
 */

/**
 * System roles constants
 */
export const SYSTEM_ROLES = {
  SUPER_ADMIN: 'Super Administrator',
  ADMIN: 'Administrator',
  MANAGER: 'Manager',
  USER: 'User',
  VIEWER: 'Viewer',
} as const;

/**
 * Application modules for permission management
 */
export const MODULES = {
  USERS: 'users',
  ROLES: 'roles',
  PERMISSIONS: 'permissions',
  TENANTS: 'tenants',
  CAMERAS: 'cameras',
  FACE_RECOGNITION: 'face_recognition',
  FACE_IMAGES: 'face_images',
  ATTENDANCE: 'attendance',
  REPORTS: 'reports',
  SETTINGS: 'settings',
  EDGE_DEVICES: 'edge_devices',
  EDGE_DEVICE_INFO: 'edge_device_info',
  EDGE_DEVICE_LOGS: 'edge_device_logs',
  SHIFTS: 'shifts',
  SHIFT_DETAILS: 'shift_details',
  UNITS: 'units',
  MEMBER_ROLES: 'member_roles',
  POLICIES: 'policies',
  DASHBOARD: 'dashboard',
  ORGANIZATIONS: 'organizations',
  STORAGE: 'storage',
} as const;

/**
 * Permission actions
 */
export const ACTIONS = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  MANAGE: 'manage',
  VIEW: 'view',
  EXPORT: 'export',
  IMPORT: 'import',
} as const;

/**
 * API endpoints mapping for permissions
 */
export const API_ENDPOINTS = {
  // User Management
  USERS: '/api/users',
  USER_PROFILE: '/api/identity/me',

  // Role & Permission Management
  ROLES: '/api/roles',
  PERMISSIONS: '/api/permissions',
  MEMBER_ROLES: '/api/member-roles',
  POLICIES: '/api/policies',

  // Organization Management
  TENANTS: '/api/tenants',
  UNITS: '/api/units',

  // Camera & Device Management
  CAMERAS: '/api/cameras',
  EDGE_DEVICES: '/api/edge-devices',
  EDGE_DEVICE_INFO: '/api/edge-device-info',
  EDGE_DEVICE_LOGS: '/api/edge-device-logs',

  // Face Recognition
  FACE_IMAGES: '/api/face-images',
  FACE_RECOGNITION_LOGS: '/api/face-recognition-logs',

  // Attendance & Shifts
  ATTENDANCE_SUMMARIES: '/api/attendance-summaries',
  SHIFTS: '/api/shifts',
  SHIFT_DETAILS: '/api/shift-details',

  // Storage
  STORAGE: '/api/storage',

  // Authentication
  AUTH_LOGIN: '/api/identity/login',
  AUTH_LOGOUT: '/api/identity/logout',
  AUTH_REFRESH: '/api/identity/refresh-token',
  AUTH_VERIFY: '/api/identity/verify-token',
} as const;

/**
 * Web routes mapping for UI permissions
 */
export const WEB_ROUTES = {
  // Dashboard
  DASHBOARD: '/dashboard',

  // User Management
  MEMBERS: '/members',
  ROLES: '/roles',
  ROLES_PERMISSION: '/roles/permission',

  // Organization Management
  ORGANIZATIONS: '/system/organizations/',
  ORGANIZATIONS_DETAIL: '/system/organizations/detail',
  UNITS: '/units',

  // Camera & Device Management
  CAMERAS: '/cameras',
  DEVICES: '/edge-devices',
  DEVICES_DETAIL: '/edge-devices/detail',
  DEVICES_ONBOARD: '/edge-devices/onboard',

  // Settings
  SETTINGS_ACCOUNT: '/settings/account',

  // Authentication
  AUTH_LOGIN: '/auth/login',
  AUTH_FORGOT_PASSWORD: '/auth/forgot-password',
} as const;

/**
 * Feature flags for different parts of the application
 */
export const FEATURES = {
  // Core features
  USER_MANAGEMENT: 'user_management',
  ROLE_MANAGEMENT: 'role_management',
  PERMISSION_MANAGEMENT: 'permission_management',

  // Organization features
  TENANT_MANAGEMENT: 'tenant_management',
  UNIT_MANAGEMENT: 'unit_management',

  // Camera & Device features
  CAMERA_MANAGEMENT: 'camera_management',
  DEVICE_MANAGEMENT: 'device_management',
  DEVICE_MONITORING: 'device_monitoring',

  // Face Recognition features
  FACE_RECOGNITION: 'face_recognition',
  FACE_IMAGE_MANAGEMENT: 'face_image_management',

  // Attendance features
  ATTENDANCE_TRACKING: 'attendance_tracking',
  ATTENDANCE_REPORTS: 'attendance_reports',
  SHIFT_MANAGEMENT: 'shift_management',

  // Storage features
  STORAGE_MANAGEMENT: 'storage_management',
  BUCKET_MANAGEMENT: 'bucket_management',
  FILE_MANAGEMENT: 'file_management',

  // Dashboard features
  DASHBOARD_VIEW: 'dashboard_view',
  ANALYTICS: 'analytics',
  REPORTS: 'reports',

  // Settings
  SYSTEM_SETTINGS: 'system_settings',
  ACCOUNT_SETTINGS: 'account_settings',
} as const;

/**
 * Resource types for fine-grained permissions
 */
export const RESOURCE_TYPES = {
  OWN: 'own',
  TEAM: 'team',
  ORGANIZATION: 'organization',
  ALL: 'all',
  SYSTEM: 'system',
} as const;

/**
 * Policy types
 */
export const POLICY_TYPES = {
  ROLE: 'role',
  PERMISSION: 'permission',
  RESOURCE: 'resource',
  CUSTOM: 'custom',
  TIME_BASED: 'time_based',
  LOCATION_BASED: 'location_based',
} as const;

/**
 * Policy effects
 */
export const POLICY_EFFECTS = {
  ALLOW: 'allow',
  DENY: 'deny',
} as const;

/**
 * Type definitions for better TypeScript support
 */
export type SystemRole = typeof SYSTEM_ROLES[keyof typeof SYSTEM_ROLES];
export type Module = typeof MODULES[keyof typeof MODULES];
export type Action = typeof ACTIONS[keyof typeof ACTIONS];
export type Feature = typeof FEATURES[keyof typeof FEATURES];
export type ResourceType = typeof RESOURCE_TYPES[keyof typeof RESOURCE_TYPES];
export type PolicyType = typeof POLICY_TYPES[keyof typeof POLICY_TYPES];
export type PolicyEffect = typeof POLICY_EFFECTS[keyof typeof POLICY_EFFECTS];
export type ApiEndpoint = typeof API_ENDPOINTS[keyof typeof API_ENDPOINTS];
export type WebRoute = typeof WEB_ROUTES[keyof typeof WEB_ROUTES];
