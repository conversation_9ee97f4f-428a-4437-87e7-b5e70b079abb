# 📋 C-CAM Cloud Platform - Project Overview

## 🏗️ **KIẾN TRÚC TỔNG THỂ**

**C-CAM Cloud Platform** là một hệ thống quản lý camera và nhận diện khuôn mặt được xây dựng theo kiến trúc **monorepo** sử dụng **Turborepo**. Đ<PERSON><PERSON> là một nền tảng đám mây toàn diện cho việc quản lý camera, thiết bị edge, và hệ thống chấm công thông minh.

### 🎯 **CHỨC NĂNG CHÍNH**

#### 1. **Quản lý Camera & Thiết bị Edge**
- 📹 Quản lý camera và thiết bị edge device
- 📊 <PERSON> dõi logs và thông tin thiết bị
- 🖼️ Quản lý hình ảnh khuôn mặt và nhận diện

#### 2. **Hệ thống Chấm công**
- 👤 Chấm công bằng nhận diện khuôn mặt
- ⏰ <PERSON>uả<PERSON> lý ca làm việc (shifts)
- 📈 Báo cáo tổng hợp chấm công hàng ngày

#### 3. **Quản lý Tổ chức & Người dùng**
- 🏢 Hệ thống multi-tenant
- 👥 Quản lý đơn vị (units), thành viên
- 🔐 Hệ thống phân quyền RBAC (Role-Based Access Control)

#### 4. **Giám sát & Tracing**
- 🔍 Distributed tracing với OpenTelemetry
- 📊 Monitoring với Prometheus & Grafana
- 📝 Audit trail và activity logs

## 🏛️ **CẤU TRÚC PROJECT**

### **Apps (Ứng dụng chính)**
```
apps/
├── api/          # Backend API server sử dụng Hono.js
└── web/          # Frontend React app với TanStack Router
```

### **Packages (Thư viện chia sẻ)**
```
packages/
├── core/                # Core framework với DI container, decorators, middleware
├── types/               # TypeScript type definitions
├── logger/              # Shared logging functionality
├── shared/              # Utilities và components dùng chung
├── tracing-node/        # Server-side tracing
├── tracing-browser/     # Client-side tracing
├── eslint/              # ESLint configurations
└── tsconfig/            # TypeScript configurations
```

## 🛠️ **TECHNOLOGY STACK**

### **Backend**
- **Framework**: Hono.js với Node.js
- **Database**: MongoDB với Mongoose ODM
- **Cache**: Redis
- **Authentication**: JWT với bcrypt
- **File Storage**: MinIO (S3-compatible)
- **API Architecture**: RESTful APIs

### **Frontend**
- **Framework**: React 19
- **Routing**: TanStack Router
- **UI Library**: Radix UI components
- **Styling**: TailwindCSS
- **State Management**: TanStack Query (React Query)
- **Forms**: React Hook Form với Zod validation

### **DevOps & Monitoring**
- **Containerization**: Docker & Docker Compose
- **Tracing**: OpenTelemetry + Jaeger
- **Metrics**: Prometheus + Grafana
- **Package Manager**: PNPM Workspaces
- **Build Tool**: Turborepo

## 📊 **DATABASE MODELS**

### **Core Entities**

#### **Users Management**
- **Users**: Quản lý người dùng với thông tin cá nhân, authentication
- **Tenants**: Multi-tenant architecture
- **Units**: Đơn vị tổ chức
- **MemberRoles**: Vai trò thành viên trong tổ chức

#### **Camera & Device Management**
- **Cameras**: Quản lý camera với IP, RTSP URL, preview images
- **EdgeDevices**: Thiết bị edge computing
- **EdgeDeviceInfo**: Thông tin chi tiết thiết bị
- **EdgeDeviceLogs**: Logs hoạt động thiết bị

#### **Face Recognition System**
- **FaceImages**: Lưu trữ hình ảnh khuôn mặt
- **FaceRecognitionLogs**: Logs nhận diện khuôn mặt

#### **Attendance Management**
- **Shifts**: Quản lý ca làm việc
- **ShiftDetails**: Chi tiết ca làm việc
- **DailyAttendanceSummaries**: Báo cáo chấm công hàng ngày

#### **Security & Authorization**
- **Roles**: Vai trò hệ thống
- **Permissions**: Quyền hạn
- **Policies**: Chính sách bảo mật
- **AuditTrailLogs**: Audit trail cho security

#### **File Management**
- **Files**: Metadata file
- **Buckets**: Storage buckets (MinIO)

#### **System Logs**
- **ActivityLogs**: Logs hoạt động người dùng
- **Tokens**: Quản lý JWT tokens

## 🔌 **API ENDPOINTS**

### **Authentication & Authorization**
```
POST   /auth/login           # User login
POST   /auth/logout          # User logout
POST   /auth/refresh         # Refresh token
GET    /auth/me              # Get current user info
```

### **User Management**
```
GET    /users                # List users
POST   /users                # Create user
GET    /users/:id            # Get user by ID
PUT    /users/:id            # Update user
DELETE /users/:id            # Delete user
```

### **Tenant & Organization**
```
GET    /tenants              # List tenants
POST   /tenants              # Create tenant
GET    /units                # List units
POST   /units                # Create unit
```

### **Camera Management**
```
GET    /cameras              # List cameras
POST   /cameras              # Create camera
GET    /cameras/:id          # Get camera by ID
PUT    /cameras/:id          # Update camera
DELETE /cameras/:id          # Delete camera
```

### **Device Management**
```
GET    /devices              # List edge devices
POST   /devices              # Create device
GET    /devices/:id/info     # Get device info
GET    /devices/:id/logs     # Get device logs
```

### **Face Recognition**
```
GET    /face-images          # List face images
POST   /face-images          # Upload face image
GET    /face-recognition-logs # List recognition logs
```

### **Attendance System**
```
GET    /shifts               # List shifts
POST   /shifts               # Create shift
GET    /shift-details        # List shift details
GET    /daily-attendance-summaries # Get attendance reports
```

### **File Storage**
```
POST   /storage/upload       # Upload file
GET    /storage/download/:id # Download file
DELETE /storage/:id          # Delete file
```

### **Monitoring & Tracing**
```
GET    /health               # Health check
GET    /traces               # Get tracing data
```

## 🐳 **DEPLOYMENT**

### **Development Environment**
```bash
# 1. Install dependencies
pnpm install

# 2. Start infrastructure (MongoDB, Redis)
docker-compose up -d

# 3. Start development servers
pnpm dev  # API: localhost:8080, Web: localhost:3000
```

### **Build Commands**
```bash
# Build all apps and packages
pnpm build

# Build specific apps
pnpm build:api          # Build API only
pnpm build:web          # Build web app only

# Development commands
pnpm dev:api            # Start API dev server
pnpm dev:web            # Start web dev server
```

### **Production Deployment Options**

#### **1. Standalone Deployment**
- Single container với web + API
- Port: 3000 (combined)
- Suitable for small to medium deployments

#### **2. Microservices Deployment**
- Separate containers cho API và Web
- API Port: 8080
- Web Port: 3000
- Better for scalability

#### **3. Infrastructure Services**
- **MongoDB**: Database (Port 27017)
- **Redis**: Cache (Port 6379)
- **Jaeger**: Tracing UI (Port 16686)
- **Prometheus**: Metrics (Port 9090)
- **Grafana**: Dashboards (Port 3002)

## 🔒 **SECURITY FEATURES**

### **Authentication & Authorization**
- 🔑 JWT-based authentication với refresh tokens
- 👤 RBAC với roles và permissions
- 🏢 Multi-tenant isolation
- 🔐 Password hashing với bcrypt

### **Security Monitoring**
- 📝 Comprehensive audit trail logs
- 👁️ Activity tracking cho tất cả user actions
- 🔍 Distributed tracing cho security events
- 🚨 Real-time monitoring và alerting

### **Data Protection**
- 🛡️ Secure file storage với MinIO
- 🌐 CORS configuration cho cross-origin requests
- 🔒 Input validation với Zod schemas
- 📊 Data encryption in transit và at rest

## 📈 **MONITORING & OBSERVABILITY**

### **Distributed Tracing**
- **OpenTelemetry**: Industry-standard tracing
- **Jaeger**: Tracing backend và UI
- **Cross-service tracing**: End-to-end request tracking
- **Performance monitoring**: Latency và error tracking

### **Metrics & Monitoring**
- **Prometheus**: Metrics collection và storage
- **Grafana**: Visualization dashboards
- **Health checks**: Automated service health monitoring
- **Alerting**: Real-time alerts cho critical issues

### **Logging**
- **Structured logging**: JSON-formatted logs
- **Request/response logging**: Complete HTTP transaction logs
- **Error tracking**: Comprehensive error logging
- **Log aggregation**: Centralized log management

## 🎯 **ĐIỂM MẠNH CỦA ARCHITECTURE**

### **1. Scalability**
- ⚡ Monorepo với shared packages, dễ dàng scale
- 🔄 Microservices-ready architecture
- 📈 Horizontal scaling support

### **2. Developer Experience**
- 🔥 Hot reload cho development
- 🧹 Automated linting và formatting
- 📝 Full TypeScript với shared types
- 🛠️ Comprehensive tooling setup

### **3. Type Safety**
- 🎯 End-to-end type safety
- 🔗 Shared types across frontend và backend
- ✅ Compile-time error detection

### **4. Observability**
- 👁️ Comprehensive monitoring stack
- 🔍 Distributed tracing
- 📊 Real-time metrics và dashboards

### **5. Security**
- 🛡️ Multi-layered security approach
- 🔐 Enterprise-grade authentication
- 📝 Complete audit trail

### **6. Maintainability**
- 🏗️ Clean architecture với DI container
- 📦 Modular package structure
- 🧪 Testable codebase design

## 🚀 **GETTING STARTED**

### **Prerequisites**
- Node.js (v18+)
- PNPM (v10.11.0)
- Docker và Docker Compose

### **Quick Start**
```bash
# Clone repository
git clone <repository-url>
cd c-cam

# Install dependencies
pnpm install

# Start development environment
docker-compose up -d
pnpm dev
```

### **Access Points**
- **Web App**: http://localhost:3000
- **API**: http://localhost:8080
- **API Health**: http://localhost:8080/health
- **Jaeger UI**: http://localhost:16686
- **Grafana**: http://localhost:3002

---

**C-CAM Cloud Platform** là một hệ thống enterprise-grade với architecture hiện đại, phù hợp cho việc quản lý camera và chấm công thông minh quy mô lớn. Hệ thống được thiết kế để đáp ứng các yêu cầu về performance, security, và scalability của các tổ chức lớn.
