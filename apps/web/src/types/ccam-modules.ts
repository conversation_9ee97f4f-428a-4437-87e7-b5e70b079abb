import { Scopes } from '@/components/Scope'
import { IdIcon, Settings02Icon } from '@/components/shared/icons'

export const CCAM_MODULES = {
  attendance: {
    key: 'attendance',
    title: 'Chấm công',
    icon: IdIcon,
    desc: '<PERSON><PERSON> <PERSON>hận và quản lý thời gian làm việc tự động',
    path: '/attendance',
    defaultPath: '/attendance/attendance-calendar',
    permissions: [
      Scopes.attendance,
    ]
  },
  // accessControl: {
  //   key: 'access-control',
  //   title: 'Kiểm soát ra vào',
  //   icon: AccessIcon,
  //   desc: 'Quản lý quyền truy cập, giám sát hoạt động ra vào',
  //   path: '/app/access-control',
  //   defaultPath: '/app/attendance/attendance-calendar'
  // },
  // securityControl: {
  //   key: 'security-control',
  //   title: '<PERSON>i<PERSON><PERSON> sát an ninh',
  //   icon: CctvCameraIcon,
  //   desc: 'B<PERSON><PERSON> v<PERSON> doanh nghiệp với gi<PERSON>m sát thông minh',
  //   path: '/app/security-control',
  //   defaultPath: '/app/attendance/attendance-calendar'
  // },
  systemManagement: {
    key: 'systemManagement',
    title: 'Quản trị hệ thống',
    icon: Settings02Icon,
    desc: 'Quản lý, cấu hình và tối ưu hệ thống',
    path: '/system',
    defaultPath: '/system/organizations/',
    permissions: [
      Scopes.identity,
    ]
  }
} as const

export type ModuleKey = keyof typeof CCAM_MODULES
export type Module = typeof CCAM_MODULES[ModuleKey]
