import { Scopes } from '@/components/Scope'
import { IdIcon, Settings02Icon } from '@/components/shared/icons'

export const CCAM_MODULES = {
  attendance: {
    key: 'attendance',
    title: 'Chấm công',
    icon: IdIcon,
    desc: '<PERSON><PERSON> nhận và quản lý thời gian làm việc tự động',
    path: '/app/attendance/attendance-calendar',
    defaultPath: '/app/attendance/attendance-calendar',
    permissions: [
      Scopes.attendance,
    ]
  },
  // accessControl: {
  //   key: 'access-control',
  //   title: 'Kiểm soát ra vào',
  //   icon: AccessIcon,
  //   desc: 'Quản lý quyền truy cập, giám sát hoạt động ra vào',
  //   path: '/app/access-control',
  //   defaultPath: '/app/attendance/attendance-calendar'
  // },
  // securityControl: {
  //   key: 'security-control',
  //   title: '<PERSON><PERSON><PERSON><PERSON> sát an ninh',
  //   icon: CctvCameraIcon,
  //   desc: '<PERSON><PERSON><PERSON> vệ doanh nghiệp với giám sát thông minh',
  //   path: '/app/security-control',
  //   defaultPath: '/app/attendance/attendance-calendar'
  // },
  systemManagement: {
    key: 'systemManagement',
    title: 'Quản trị hệ thống',
    icon: Settings02Icon,
    desc: 'Quản lý, cấu hình và tối ưu hệ thống',
    path: '/app/system-management',
    defaultPath: '/system/organizations/',
    permissions: [
      Scopes.identity,
    ]
  }
} as const

export type ModuleKey = keyof typeof CCAM_MODULES
export type Module = typeof CCAM_MODULES[ModuleKey]
