import ProtectedLayout from '@/components/layout/protected.layout'
import {
  PlusIcon,
  SetupDeviceStep1Icon,
  SetupDeviceStep2Icon,
} from '@/components/shared/icons'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { createFileRoute, useNavigate } from '@tanstack/react-router'

function AddDevicePage() {
  const navigate = useNavigate()

  const handleBack = () => {
    navigate({ to: '/edge-devices' })
  }

  return (
    <ProtectedLayout>
      <div className="flex h-screen bg-[#F5F6F7]">
        {/* Main content */}
        <div className="flex-1 px-6 py-5">
          {/* Header */}
          <Breadcrumb className="text-xs pb-5">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink
                  onClick={handleBack}
                  className="text-[#8f959e] cursor-pointer"
                >
                  Danh sách toà nhà
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="text-[#67718e]" />
              <BreadcrumbItem>
                <BreadcrumbLink className="text-[#1f2329] cursor-default">
                  Chi tiết toà nhà
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          {/* Content area - Empty for now */}
          <div className="rounded-lg">
            <div className="flex justify-start items-stretch self-stretch flex-grow-0 flex-shrink-0 gap-4 h-full">
              {/* Part 1 - Always 50% width and equal height */}
              <div className="flex flex-col justify-start items-start flex-1 gap-4 px-6 py-5 rounded-lg bg-white border border-[#dde4ee]">
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                  <div className="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]" />
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2">
                    <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]">
                      Danh sách thiết bị
                    </p>
                  </div>
                </div>
                <div className="flex flex-col justify-center items-center self-stretch flex-grow gap-4 py-12 px-2">
                  <div className="flex flex-col justify-center items-center w-full gap-1">
                    <div className="flex flex-col justify-start items-center w-full relative overflow-hidden gap-[9px]">
                      <p className="text-base font-semibold text-center text-[#1f2329] break-words">
                        Chưa thiết lập thiết bị biên
                      </p>
                    </div>
                    <div className="flex justify-center items-center w-full relative bg-white">
                      <p className="text-sm text-center text-[#1f2329] break-words leading-relaxed">
                        Máy tính của bạn đang không kết nối tới bất kì thiết bị biên nào
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-center items-center w-full gap-2">
                    <div className="flex justify-center items-center">
                      <div className="flex justify-center items-center relative gap-2 px-3 py-2 rounded-md bg-[#008fd3] hover:bg-[#007bb8] transition-colors cursor-pointer">
                        <PlusIcon fillColor="white" />
                        <p className="text-xs font-medium text-center text-white whitespace-nowrap">
                          Thêm thiết bị
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Part 2 - Always 50% width and equal height */}
              <div className="flex flex-col justify-start items-start flex-1 gap-6 px-6 py-5 rounded-lg bg-white border border-[#dde4ee]">
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                  <div className="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]" />
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2">
                    <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]">
                      Hướng dẫn thiết lập
                    </p>
                  </div>
                </div>
                <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-6">
                  <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-3">
                    <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative overflow-hidden gap-[3px]">
                      <p className="flex-grow text-[13px] font-medium text-left text-[#1f2329]">
                        1, Đảm bảo các thiết bị đang kết nối cùng một mạng LAN
                        hoặc cùng dải mạng IP.
                      </p>
                    </div>
                    <div className="w-full flex justify-center">
                      <div className="max-w-full">
                        <SetupDeviceStep1Icon className="w-full h-auto max-w-[520px]" />
                      </div>
                    </div>
                    <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-3">
                      <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative overflow-hidden gap-[3px]">
                        <p className="flex-grow text-[13px] font-medium text-left text-[#1f2329]">
                          2, Kết nối thiết bị biên và AI Server thông qua máy
                          tính của bạn
                        </p>
                      </div>
                      <div className="w-full flex justify-center">
                        <SetupDeviceStep2Icon className="w-full h-auto max-w-[520px]" />
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-1">
                    <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative overflow-hidden gap-[3px]">
                      <p className="flex-grow text-[13px] font-medium text-left text-[#1f2329]">
                        3, Thiết lập thông tin cho thiết bị biên
                      </p>
                    </div>
                    <p className="self-stretch flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                      Lựa chọn và thiết lập các thông tin cơ bản của thiết bị
                      biên.
                    </p>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 self-stretch relative gap-1">
                    <p className="self-stretch flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                      4, Hoàn thành
                    </p>
                    <p className="self-stretch flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                      Bạn đã hoàn thành các bước kết nối thiết bị biên với hệ
                      thống C-CAM.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}

export const Route = createFileRoute('/edge-devices/add-device')({
  component: AddDevicePage,
})
