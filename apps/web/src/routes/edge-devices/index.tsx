import ProtectedLayout from '@/components/layout/protected.layout'
import { DataTable } from '@/components/shared/data-table/table'
import { DeleteDeviceDialog } from '@/components/shared/delete-device-dialog'
import {
  EdgeDeviceIcon,
  FilterIcon,
  MenuDotsIcon,
  PlusIcon
} from '@/components/shared/icons'
import { RestartDeviceDialog } from '@/components/shared/restart-device-dialog'
import type { SelectionAction } from '@/components/shared/selection-bar'
import { SelectionBar } from '@/components/shared/selection-bar'
import { UpdateFirmwareDialog } from '@/components/shared/update-firmware-dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useTableSelection } from '@/shared/hooks/use-table-selection'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { ColumnDef } from '@tanstack/react-table'
import { Download, RotateCcw, Trash2 } from 'lucide-react'
import { useMemo, useState } from 'react'

// Types
interface EdgeDevice {
  id: string
  name: string
  type: 'FACE_TERMINAL' | 'AI_BOX'
  status: 'ONLINE' | 'OFFLINE' | 'UPDATING'
  ipAddress: string
  firmwareVersion: string
  uptime: string
  lastSignal: string
  syncPercentage: number
}

// Mock data
const mockEdgeDevices: EdgeDevice[] = [
  {
    id: '1',
    name: '(HN) AI BOX - CTS Demo 1',
    type: 'AI_BOX',
    status: 'ONLINE',
    ipAddress: '*************',
    firmwareVersion: '1.27',
    uptime: '10 giờ',
    lastSignal: '27/06/2024 10:11:58',
    syncPercentage: 100,
  },
  {
    id: '2',
    name: '(HN) AI BOX - CTS Demo 2',
    type: 'AI_BOX',
    status: 'ONLINE',
    ipAddress: '*************',
    firmwareVersion: '1.27',
    uptime: '8 giờ 30 phút',
    lastSignal: '27/06/2024 10:05:42',
    syncPercentage: 98,
  },
  {
    id: '3',
    name: '(HN) Face Terminal - Cổng A',
    type: 'FACE_TERMINAL',
    status: 'ONLINE',
    ipAddress: '*************',
    firmwareVersion: '2.15',
    uptime: '15 giờ 20 phút',
    lastSignal: '27/06/2024 10:10:15',
    syncPercentage: 95,
  },
  {
    id: '4',
    name: '(HN) AI BOX - CTS Demo 3',
    type: 'AI_BOX',
    status: 'OFFLINE',
    ipAddress: '*************',
    firmwareVersion: '1.25',
    uptime: '0 giờ',
    lastSignal: '26/06/2024 18:30:22',
    syncPercentage: 0,
  },
  {
    id: '5',
    name: '(HN) Face Terminal - Cổng B',
    type: 'FACE_TERMINAL',
    status: 'UPDATING',
    ipAddress: '*************',
    firmwareVersion: '2.12',
    uptime: '2 giờ 15 phút',
    lastSignal: '27/06/2024 09:45:33',
    syncPercentage: 75,
  },
  {
    id: '6',
    name: '(HN) AI BOX - CTS Demo 4',
    type: 'AI_BOX',
    status: 'ONLINE',
    ipAddress: '*************',
    firmwareVersion: '1.28',
    uptime: '12 giờ 45 phút',
    lastSignal: '27/06/2024 10:12:10',
    syncPercentage: 100,
  },
  {
    id: '7',
    name: '(HN) Face Terminal - Cổng C',
    type: 'FACE_TERMINAL',
    status: 'UPDATING',
    ipAddress: '*************',
    firmwareVersion: '2.14',
    uptime: '6 giờ 20 phút',
    lastSignal: '27/06/2024 10:08:45',
    syncPercentage: 95,
  },
  {
    id: '8',
    name: '(HN) AI BOX - CTS Demo 5',
    type: 'AI_BOX',
    status: 'OFFLINE',
    ipAddress: '*************',
    firmwareVersion: '1.26',
    uptime: '0 giờ',
    lastSignal: '26/06/2024 16:22:11',
    syncPercentage: 0,
  },
]

// Status badge component
const StatusBadge = ({ status }: { status: EdgeDevice['status'] }) => {
  const getStatusConfig = (status: EdgeDevice['status']) => {
    switch (status) {
      case 'ONLINE':
        return {
          label: 'Đang hoạt động',
          dotColor: 'bg-[#35c724]',
          textColor: 'text-[#1f2329]',
        }
      case 'OFFLINE':
        return {
          label: 'Ngoại tuyến',
          dotColor: 'bg-[#8f959e]',
          textColor: 'text-[#1f2329]',
        }
      case 'UPDATING':
        return {
          label: 'Đang cập nhật',
          dotColor: 'bg-yellow-500',
          textColor: 'text-[#1f2329]',
        }
      default:
        return {
          label: 'Không xác định',
          dotColor: 'bg-gray-500',
          textColor: 'text-[#1f2329]',
        }
    }
  }

  const config = getStatusConfig(status)
  return (
    <div className="flex justify-start items-center gap-1.5">
      <div className={`w-2 h-2 rounded-lg ${config.dotColor}`} />
      <span className={`text-sm ${config.textColor}`}>{config.label}</span>
    </div>
  )
}

// Get device type label
const getDeviceTypeLabel = (type: EdgeDevice['type']) => {
  switch (type) {
    case 'AI_BOX':
      return 'AI Box'
    case 'FACE_TERMINAL':
      return 'Face Terminal'
    default:
      return 'Unknown'
  }
}

function EdgeDevicesPage() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [tempStatusFilter, setTempStatusFilter] = useState<string>('all')
  const [tempTypeFilter, setTempTypeFilter] = useState<string>('all')

  // Dialog states
  const [restartDialogOpen, setRestartDialogOpen] = useState(false)
  const [updateFirmwareDialogOpen, setUpdateFirmwareDialogOpen] =
    useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [singleDeviceForAction, setSingleDeviceForAction] =
    useState<EdgeDevice | null>(null)

  // Use the table selection hook
  const {
    selectedCount,
    hasSelection,
    selectedIds,
    handleSelectionChange,
    getRowSelectionState,
    clearSelection,
  } = useTableSelection<EdgeDevice>({
    getRowId: (device) => device.id,
    persistAcrossPages: true,
  })

  // Handle navigation
  const handleAddDevice = () => {
    navigate({ to: '/edge-devices/add-device' })
  }

  // Handle device actions
  const handleEditDevice = (device: EdgeDevice) => {
    console.log('Edit device:', device.id)
  }

  const handleRestart = (device: EdgeDevice) => {
    setSingleDeviceForAction(device)
    setRestartDialogOpen(true)
  }

  const handleUpdateFirmware = (device: EdgeDevice) => {
    setSingleDeviceForAction(device)
    setUpdateFirmwareDialogOpen(true)
  }

  const handleDeleteDevice = (device: EdgeDevice) => {
    setSingleDeviceForAction(device)
    setDeleteDialogOpen(true)
  }



  // Table columns
  const columns: ColumnDef<EdgeDevice>[] = [
    {
      accessorKey: 'name',
      header: 'Thiết bị',
      cell: ({ row }) => (
        <div className="flex justify-center items-center gap-2">
          <EdgeDeviceIcon />
          <div className="flex flex-col justify-center items-start flex-grow">
            <span className="text-sm font-semibold text-[#26282c]">
              {row.original.name}
            </span>
            <span className="text-xs font-medium text-[#73787e]">
              {getDeviceTypeLabel(row.original.type)} - {row.original.ipAddress}
            </span>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'firmwareVersion',
      header: 'Phiên bản',
      cell: ({ row }) => (
        <span className="text-sm text-[#1f2329]">
          {row.original.firmwareVersion}
        </span>
      ),
    },
    {
      accessorKey: 'uptime',
      header: 'Thời gian chạy',
      cell: ({ row }) => (
        <span className="text-sm text-[#1f2329]">{row.original.uptime}</span>
      ),
    },
    {
      accessorKey: 'lastSignal',
      header: 'Tín hiệu lần cuối',
      cell: ({ row }) => (
        <span className="text-sm text-[#1f2329]">
          {row.original.lastSignal}
        </span>
      ),
    },
    {
      accessorKey: 'syncPercentage',
      header: 'Đồng bộ',
      cell: ({ row }) => (
        <span className="text-sm text-[#1f2329]">
          {row.original.syncPercentage}%
        </span>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Trạng thái',
      cell: ({ row }) => <StatusBadge status={row.original.status} />,
    },
    {
      id: 'actions',
      header: 'Thao tác',
      cell: ({ row }) => (
        <div className="flex justify-start items-center gap-2">
          <button className="text-sm font-medium text-[#008fd3] hover:underline">
            Chi tiết
          </button>

          <Popover>
            <PopoverTrigger asChild>
              <button
                className="p-1 hover:bg-gray-200 rounded transition-colors cursor-pointer"
                onClick={(e) => e.stopPropagation()}
              >
                <MenuDotsIcon className="w-3 h-3" />
              </button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-2" align="end">
              <div className="space-y-1">
                <button
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                  onClick={() => handleEditDevice(row.original)}
                >
                  Chỉnh sửa
                </button>
                <button
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                  onClick={() => handleRestart(row.original)}
                >
                  Khởi động lại
                </button>
                <button
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                  onClick={() => handleUpdateFirmware(row.original)}
                >
                  Cập nhật phiên bản
                </button>
                <button
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer text-red-600"
                  onClick={() => handleDeleteDevice(row.original)}
                >
                  Xóa thiết bị
                </button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      ),
    },
  ]

  // Filter data
  const filteredDevices = useMemo(() => {
    return mockEdgeDevices.filter((device) => {
      const matchesSearch =
        device.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        getDeviceTypeLabel(device.type)
          .toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        device.ipAddress.includes(searchTerm)

      const matchesStatus =
        statusFilter === 'all' || device.status === statusFilter
      const matchesType = typeFilter === 'all' || device.type === typeFilter

      return matchesSearch && matchesStatus && matchesType
    })
  }, [searchTerm, statusFilter, typeFilter])

  // Filter button text
  const getFilterButtonText = () => {
    const filters = []
    if (statusFilter !== 'all') {
      const statusLabels = {
        ONLINE: 'Đang hoạt động',
        OFFLINE: 'Ngoại tuyến',
        UPDATING: 'Đang cập nhật',
      }
      filters.push(statusLabels[statusFilter as keyof typeof statusLabels])
    }
    if (typeFilter !== 'all') {
      const typeLabels = {
        AI_BOX: 'AI Box',
        FACE_TERMINAL: 'Face Terminal',
      }
      filters.push(typeLabels[typeFilter as keyof typeof typeLabels])
    }
    return filters.length > 0 ? filters.join(', ') : 'Bộ lọc'
  }

  const applyFilters = () => {
    setStatusFilter(tempStatusFilter)
    setTypeFilter(tempTypeFilter)
  }

  const clearFilters = () => {
    setTempStatusFilter('all')
    setTempTypeFilter('all')
    setStatusFilter('all')
    setTypeFilter('all')
  }

  // Handle bulk actions
  const handleBulkRestart = () => {
    setSingleDeviceForAction(null)
    setRestartDialogOpen(true)
  }

  const handleBulkUpdateFirmware = () => {
    setSingleDeviceForAction(null)
    setUpdateFirmwareDialogOpen(true)
  }

  const handleBulkDelete = () => {
    setSingleDeviceForAction(null)
    setDeleteDialogOpen(true)
  }

  // Handle confirmation actions
  const handleRestartConfirm = (deviceIds: string[]) => {
    console.log('Restart confirmed for devices:', deviceIds)
    // TODO: Call API to restart devices
    if (!singleDeviceForAction) {
      clearSelection()
    }
  }

  const handleUpdateFirmwareConfirm = (deviceIds: string[]) => {
    console.log('Update firmware confirmed for devices:', deviceIds)
    // TODO: Call API to update firmware
    if (!singleDeviceForAction) {
      clearSelection()
    }
  }

  const handleDeleteConfirm = (deviceIds: string[]) => {
    console.log('Delete confirmed for devices:', deviceIds)
    // TODO: Call API to delete devices
    if (!singleDeviceForAction) {
      clearSelection()
    }
  }

  // Define selection actions
  const selectionActions: SelectionAction[] = [
    {
      key: 'restart',
      label: 'Khởi động lại',
      icon: <RotateCcw className="h-4 w-4" />,
      onClick: handleBulkRestart,
    },
    {
      key: 'update-firmware',
      label: 'Cập nhật phiên bản',
      icon: <Download className="h-4 w-4" />,
      onClick: handleBulkUpdateFirmware,
    },
    {
      key: 'delete',
      label: 'Xóa thiết bị',
      icon: <Trash2 className="h-4 w-4" />,
      onClick: handleBulkDelete,
    },
  ]

  return (
    <ProtectedLayout>
      <div className="flex h-screen bg-white">
        {/* Main content */}
        <div className="flex-1 px-6 py-5">
          {/* Header */}
          <div className="flex justify-between items-center mb-3">
            <h1 className="text-xl font-semibold text-[#1f2329]">
              Thiết bị biên
            </h1>
            <button
              onClick={handleAddDevice}
              className="flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-white bg-[#008fd3] rounded-md hover:bg-[#007bb8] cursor-pointer"
            >
              <PlusIcon fillColor="white" />
              Thêm thiết bị
            </button>
          </div>

          {/* Search and filters */}
          <div className="flex gap-2 mb-4">
            <div className="w-[250px]">
              <Input
                type="text"
                placeholder="Tìm theo tên, loại thiết bị hoặc IP"
                className="text-xs"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 border-[#d0d3d6] text-[#1f2329] cursor-pointer"
                >
                  <FilterIcon />
                  <span className="text-xs font-medium">
                    {getFilterButtonText()}
                  </span>
                </Button>
              </PopoverTrigger>
              <PopoverContent align="start" className="w-80 p-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-[#1f2329]">
                      Trạng thái thiết bị:
                    </Label>
                    <Select
                      value={tempStatusFilter}
                      onValueChange={setTempStatusFilter}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Chọn trạng thái" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tất cả</SelectItem>
                        <SelectItem value="ONLINE">Đang hoạt động</SelectItem>
                        <SelectItem value="OFFLINE">Ngoại tuyến</SelectItem>
                        <SelectItem value="UPDATING">Đang cập nhật</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-[#1f2329]">
                      Loại thiết bị:
                    </Label>
                    <Select
                      value={tempTypeFilter}
                      onValueChange={setTempTypeFilter}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Chọn loại thiết bị" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tất cả</SelectItem>
                        <SelectItem value="AI_BOX">AI Box</SelectItem>
                        <SelectItem value="FACE_TERMINAL">
                          Face Terminal
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Filter Actions */}
                  <div className="flex justify-end">
                    <div className="flex items-center gap-2 pt-2 w-[130px]">
                      <Button
                        onClick={clearFilters}
                        variant="outline"
                        className="flex-1 border-[#d0d3d6] text-[#1f2329] text-xs font-medium"
                        size="sm"
                      >
                        Hủy bỏ
                      </Button>
                      <Button
                        onClick={applyFilters}
                        className="flex-1 bg-[#008fd3] hover:bg-[#008fd3]/90 text-white text-xs font-medium"
                        size="sm"
                      >
                        Lọc
                      </Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Selection info */}
          {hasSelection && (
            <SelectionBar
              selectedCount={selectedCount}
              actions={selectionActions}
              itemName="thiết bị"
              className="mb-4"
            />
          )}

          {/* Content area */}
          <div className="bg-white rounded-lg">
            <DataTable
              columns={columns}
              data={filteredDevices}
              showPagination={true}
              sizeChanger={true}
              enableRowSelection={true}
              onRowSelectionChange={handleSelectionChange}
              rowSelection={getRowSelectionState(filteredDevices)}
              columnWidths={{
                select: 20,
                name: 270,
                firmwareVersion: 100,
                uptime: 129,
                lastSignal: 185,
                syncPercentage: 100,
                status: 168,
                actions: 100,
              }}
            />
          </div>
        </div>

        {/* Dialogs */}
        <RestartDeviceDialog
          open={restartDialogOpen}
          onOpenChange={setRestartDialogOpen}
          selectedCount={selectedCount}
          onConfirm={handleRestartConfirm}
          deviceIds={
            singleDeviceForAction
              ? [singleDeviceForAction.id]
              : selectedIds.map(String)
          }
          singleDevice={singleDeviceForAction}
          clearSelection={clearSelection}
        />

        <UpdateFirmwareDialog
          open={updateFirmwareDialogOpen}
          onOpenChange={setUpdateFirmwareDialogOpen}
          selectedCount={selectedCount}
          onConfirm={handleUpdateFirmwareConfirm}
          deviceIds={
            singleDeviceForAction
              ? [singleDeviceForAction.id]
              : selectedIds.map(String)
          }
          singleDevice={singleDeviceForAction}
          clearSelection={clearSelection}
        />

        <DeleteDeviceDialog
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          selectedCount={selectedCount}
          onConfirm={handleDeleteConfirm}
          deviceIds={
            singleDeviceForAction
              ? [singleDeviceForAction.id]
              : selectedIds.map(String)
          }
          singleDevice={singleDeviceForAction}
          clearSelection={clearSelection}
        />
      </div>
    </ProtectedLayout>
  )
}

export const Route = createFileRoute('/edge-devices/')({
  component: EdgeDevicesPage,
})
