import ProtectedLayout from '@/components/layout/protected.layout'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { ArrowLeft, Edit, RotateCcw, Trash2 } from 'lucide-react'
import { useState } from 'react'

// Types
interface EdgeDeviceDetail {
  id: string
  name: string
  type: 'ATTENDANCE' | 'CAMERA' | 'SENSOR'
  status: 'ONLINE' | 'OFFLINE' | 'MAINTENANCE'
  ipAddress: string
  macAddress: string
  firmwareVersion: string
  location: string
  lastSeen: string
  createdAt: string
  description?: string
  manufacturer?: string
  model?: string
  serialNumber?: string
  port?: number
  protocol?: string
  uptime?: string
  cpuUsage?: number
  memoryUsage?: number
  diskUsage?: number
  temperature?: number
}

interface DeviceLog {
  id: string
  timestamp: string
  level: 'INFO' | 'WARNING' | 'ERROR'
  message: string
  source: string
}

// Mock data
const mockDeviceDetail: EdgeDeviceDetail = {
  id: '1',
  name: 'Thiết bị chấm công A1',
  type: 'ATTENDANCE',
  status: 'ONLINE',
  ipAddress: '*************',
  macAddress: '00:1B:44:11:3A:B7',
  firmwareVersion: '1.2.3',
  location: 'Tầng 1 - Cổng chính',
  lastSeen: '2024-01-15T10:30:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  description: 'Thiết bị chấm công tại cổng chính tòa nhà',
  manufacturer: 'TechCorp',
  model: 'TC-ATT-100',
  serialNumber: 'TC100-2024-001',
  port: 8080,
  protocol: 'HTTP',
  uptime: '15 ngày 8 giờ 30 phút',
  cpuUsage: 25,
  memoryUsage: 60,
  diskUsage: 45,
  temperature: 42
}

const mockDeviceLogs: DeviceLog[] = [
  {
    id: '1',
    timestamp: '2024-01-15T10:30:00Z',
    level: 'INFO',
    message: 'Thiết bị khởi động thành công',
    source: 'System'
  },
  {
    id: '2',
    timestamp: '2024-01-15T10:25:00Z',
    level: 'INFO',
    message: 'Kết nối mạng ổn định',
    source: 'Network'
  },
  {
    id: '3',
    timestamp: '2024-01-15T09:15:00Z',
    level: 'WARNING',
    message: 'Nhiệt độ CPU cao: 65°C',
    source: 'Hardware'
  },
  {
    id: '4',
    timestamp: '2024-01-15T08:00:00Z',
    level: 'ERROR',
    message: 'Lỗi kết nối cơ sở dữ liệu',
    source: 'Database'
  }
]

// Status badge component
const StatusBadge = ({ status }: { status: EdgeDeviceDetail['status'] }) => {
  const getStatusConfig = (status: EdgeDeviceDetail['status']) => {
    switch (status) {
      case 'ONLINE':
        return { label: 'Trực tuyến', className: 'bg-green-100 text-green-800' }
      case 'OFFLINE':
        return { label: 'Ngoại tuyến', className: 'bg-red-100 text-red-800' }
      case 'MAINTENANCE':
        return { label: 'Bảo trì', className: 'bg-yellow-100 text-yellow-800' }
      default:
        return { label: 'Không xác định', className: 'bg-gray-100 text-gray-800' }
    }
  }

  const config = getStatusConfig(status)
  return (
    <Badge className={config.className}>
      {config.label}
    </Badge>
  )
}

// Type badge component
const TypeBadge = ({ type }: { type: EdgeDeviceDetail['type'] }) => {
  const getTypeConfig = (type: EdgeDeviceDetail['type']) => {
    switch (type) {
      case 'ATTENDANCE':
        return { label: 'Chấm công', className: 'bg-blue-100 text-blue-800' }
      case 'CAMERA':
        return { label: 'Camera', className: 'bg-purple-100 text-purple-800' }
      case 'SENSOR':
        return { label: 'Cảm biến', className: 'bg-orange-100 text-orange-800' }
      default:
        return { label: 'Khác', className: 'bg-gray-100 text-gray-800' }
    }
  }

  const config = getTypeConfig(type)
  return (
    <Badge className={config.className}>
      {config.label}
    </Badge>
  )
}

// Log level badge
const LogLevelBadge = ({ level }: { level: DeviceLog['level'] }) => {
  const getLevelConfig = (level: DeviceLog['level']) => {
    switch (level) {
      case 'INFO':
        return { label: 'Thông tin', className: 'bg-blue-100 text-blue-800' }
      case 'WARNING':
        return { label: 'Cảnh báo', className: 'bg-yellow-100 text-yellow-800' }
      case 'ERROR':
        return { label: 'Lỗi', className: 'bg-red-100 text-red-800' }
      default:
        return { label: 'Khác', className: 'bg-gray-100 text-gray-800' }
    }
  }

  const config = getLevelConfig(level)
  return (
    <Badge className={config.className}>
      {config.label}
    </Badge>
  )
}

// Format date
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('vi-VN')
}

// Progress bar component
const ProgressBar = ({ value, label }: { value: number; label: string }) => {
  const getColor = (value: number) => {
    if (value < 50) return 'bg-green-500'
    if (value < 80) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  return (
    <div className="space-y-1">
      <div className="flex justify-between text-sm">
        <span>{label}</span>
        <span>{value}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full ${getColor(value)}`}
          style={{ width: `${value}%` }}
        />
      </div>
    </div>
  )
}

function EdgeDeviceDetailPage() {
  const navigate = useNavigate()
  const [device] = useState<EdgeDeviceDetail>(mockDeviceDetail)
  const [logs] = useState<DeviceLog[]>(mockDeviceLogs)

  const handleBack = () => {
    navigate({ to: '/edge-devices' })
  }

  const handleEdit = () => {
    // TODO: Implement edit functionality
    console.log('Edit device:', device.id)
  }

  const handleRestart = () => {
    // TODO: Implement restart functionality
    console.log('Restart device:', device.id)
  }

  const handleDelete = () => {
    // TODO: Implement delete functionality
    console.log('Delete device:', device.id)
  }

  return (
    <ProtectedLayout>
      <div className="flex h-screen bg-gray-50">
        {/* Main content */}
        <div className="flex-1 px-6 py-5">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Quay lại
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-[#1f2329]">
                  {device.name}
                </h1>
                <p className="text-sm text-gray-600">{device.location}</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
                className="flex items-center gap-2"
              >
                <Edit className="h-4 w-4" />
                Chỉnh sửa
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRestart}
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Khởi động lại
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                Xóa
              </Button>
            </div>
          </div>

          {/* Content area - Flexible */}
          <div className="flex-1 min-h-0 overflow-auto">
            <Tabs defaultValue="overview" className="space-y-4">
              <TabsList>
                <TabsTrigger value="overview">Tổng quan</TabsTrigger>
                <TabsTrigger value="monitoring">Giám sát</TabsTrigger>
                <TabsTrigger value="logs">Nhật ký</TabsTrigger>
                <TabsTrigger value="settings">Cài đặt</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Basic Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Thông tin cơ bản</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Loại thiết bị:</span>
                        <TypeBadge type={device.type} />
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Trạng thái:</span>
                        <StatusBadge status={device.status} />
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Địa chỉ IP:</span>
                        <span className="text-sm font-mono">{device.ipAddress}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">MAC Address:</span>
                        <span className="text-sm font-mono">{device.macAddress}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Phiên bản:</span>
                        <span className="text-sm">{device.firmwareVersion}</span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Device Details */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Chi tiết thiết bị</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Nhà sản xuất:</span>
                        <span className="text-sm">{device.manufacturer}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Model:</span>
                        <span className="text-sm">{device.model}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Serial Number:</span>
                        <span className="text-sm font-mono">{device.serialNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Port:</span>
                        <span className="text-sm">{device.port}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Protocol:</span>
                        <span className="text-sm">{device.protocol}</span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Status Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Thông tin trạng thái</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Thời gian hoạt động:</span>
                        <span className="text-sm">{device.uptime}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Lần cuối hoạt động:</span>
                        <span className="text-sm">{formatDate(device.lastSeen)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Ngày tạo:</span>
                        <span className="text-sm">{formatDate(device.createdAt)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Nhiệt độ:</span>
                        <span className="text-sm">{device.temperature}°C</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Description */}
                {device.description && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Mô tả</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-700">{device.description}</p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="monitoring" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Hiệu suất hệ thống</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <ProgressBar value={device.cpuUsage || 0} label="CPU" />
                      <ProgressBar value={device.memoryUsage || 0} label="Bộ nhớ" />
                      <ProgressBar value={device.diskUsage || 0} label="Ổ cứng" />
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Thông tin mạng</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Trạng thái kết nối:</span>
                        <StatusBadge status={device.status} />
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Ping:</span>
                        <span className="text-sm text-green-600">12ms</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Băng thông:</span>
                        <span className="text-sm">100 Mbps</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="logs" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Nhật ký hoạt động</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {logs.map((log) => (
                        <div key={log.id} className="flex items-start gap-3 p-3 border rounded-lg">
                          <LogLevelBadge level={log.level} />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-gray-900">{log.message}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <span className="text-xs text-gray-500">{formatDate(log.timestamp)}</span>
                              <span className="text-xs text-gray-400">•</span>
                              <span className="text-xs text-gray-500">{log.source}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="settings" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Cài đặt thiết bị</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600">
                      Tính năng cài đặt thiết bị sẽ được phát triển trong phiên bản tiếp theo.
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}

export const Route = createFileRoute('/edge-devices/detail')({
  component: EdgeDeviceDetailPage,
})
