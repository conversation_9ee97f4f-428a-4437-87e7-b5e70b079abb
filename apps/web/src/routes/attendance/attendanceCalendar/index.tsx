import ProtectedLayout from '@/components/layout/protected.layout'
import { But<PERSON> } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useSelectedTenant } from '@/hooks/use-selected-tenant'
import { createFileRoute } from '@tanstack/react-router'
import { CalendarDays, Download, Search, Users } from 'lucide-react'
import { useState } from 'react'

export const Route = createFileRoute('/attendance/attendanceCalendar/')({
  component: RouteComponent,
})

interface AttendanceRecord {
  id: string
  employeeId: string
  employeeName: string
  date: string
  checkIn: string
  checkOut: string
  workingHours: number
  status: 'present' | 'absent' | 'late' | 'early_leave'
  unit: string
}

// Mock data for demonstration
const mockAttendanceData: AttendanceRecord[] = [
  {
    id: '1',
    employeeId: 'EMP001',
    employeeName: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
    date: '2024-01-15',
    checkIn: '08:00',
    checkOut: '17:30',
    workingHours: 8.5,
    status: 'present',
    unit: 'Phòng IT'
  },
  {
    id: '2',
    employeeId: 'EMP002',
    employeeName: 'Trần Thị B',
    date: '2024-01-15',
    checkIn: '08:15',
    checkOut: '17:30',
    workingHours: 8.25,
    status: 'late',
    unit: 'Phòng HR'
  },
  {
    id: '3',
    employeeId: 'EMP003',
    employeeName: 'Lê Văn C',
    date: '2024-01-15',
    checkIn: '-',
    checkOut: '-',
    workingHours: 0,
    status: 'absent',
    unit: 'Phòng Kế toán'
  }
]

function RouteComponent() {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [unitFilter, setUnitFilter] = useState<string>('all')

  const { selectedTenant } = useSelectedTenant()

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'present':
        return { label: 'Có mặt', color: 'bg-green-100 text-green-800' }
      case 'absent':
        return { label: 'Vắng mặt', color: 'bg-red-100 text-red-800' }
      case 'late':
        return { label: 'Đi muộn', color: 'bg-yellow-100 text-yellow-800' }
      case 'early_leave':
        return { label: 'Về sớm', color: 'bg-orange-100 text-orange-800' }
      default:
        return { label: 'Không xác định', color: 'bg-gray-100 text-gray-800' }
    }
  }

  const filteredData = mockAttendanceData.filter(record => {
    const matchesSearch = record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || record.status === statusFilter
    const matchesUnit = unitFilter === 'all' || record.unit === unitFilter

    return matchesSearch && matchesStatus && matchesUnit
  })

  const stats = {
    total: mockAttendanceData.length,
    present: mockAttendanceData.filter(r => r.status === 'present').length,
    absent: mockAttendanceData.filter(r => r.status === 'absent').length,
    late: mockAttendanceData.filter(r => r.status === 'late').length,
  }

  return (
    <ProtectedLayout>
      <div className="flex h-screen bg-white">
        {/* Sidebar with Calendar */}
        <div className="w-[300px] px-4 py-5 bg-white border-r border-[#e9eaf2]">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-[#1f2329] mb-3">Chọn ngày</h3>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                className="rounded-md border"
              />
            </div>

            {/* Quick Stats */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Thống kê hôm nay</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600">Tổng số:</span>
                  <span className="font-medium">{stats.total}</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600">Có mặt:</span>
                  <span className="font-medium text-green-600">{stats.present}</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600">Vắng mặt:</span>
                  <span className="font-medium text-red-600">{stats.absent}</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600">Đi muộn:</span>
                  <span className="font-medium text-yellow-600">{stats.late}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 px-6 py-5">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-xl font-semibold text-[#1f2329] flex items-center gap-2">
                <CalendarDays className="h-5 w-5" />
                Lịch chấm công
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                {selectedTenant?.name || 'Tổ chức'} - {selectedDate?.toLocaleDateString('vi-VN') || 'Chưa chọn ngày'}
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Xuất báo cáo
              </Button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1 max-w-sm">
              <Label htmlFor="search" className="text-xs font-medium text-gray-700 mb-1 block">
                Tìm kiếm
              </Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Tên nhân viên hoặc mã NV"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="w-48">
              <Label htmlFor="status-filter" className="text-xs font-medium text-gray-700 mb-1 block">
                Trạng thái
              </Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="present">Có mặt</SelectItem>
                  <SelectItem value="absent">Vắng mặt</SelectItem>
                  <SelectItem value="late">Đi muộn</SelectItem>
                  <SelectItem value="early_leave">Về sớm</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="w-48">
              <Label htmlFor="unit-filter" className="text-xs font-medium text-gray-700 mb-1 block">
                Đơn vị
              </Label>
              <Select value={unitFilter} onValueChange={setUnitFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn đơn vị" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="Phòng IT">Phòng IT</SelectItem>
                  <SelectItem value="Phòng HR">Phòng HR</SelectItem>
                  <SelectItem value="Phòng Kế toán">Phòng Kế toán</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
