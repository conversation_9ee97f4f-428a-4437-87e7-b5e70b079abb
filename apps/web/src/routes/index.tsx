import { useIdentityActions } from '@/hooks/use-identity-actions'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useEffect } from 'react'

export const Route = createFileRoute('/')({
  component: App,
})

function App() {
  const { isAuthenticated, isLoading } = useIdentityActions()
  const navigate = useNavigate()

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        navigate({ to: '/system/organizations/' })
      } else {
        navigate({ to: '/auth/login' })
      }
    }
  }, [isAuthenticated, isLoading, navigate])

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-sm text-gray-600"><PERSON><PERSON> tả<PERSON>...</p>
        </div>
      </div>
    )
  }

  return null
}
