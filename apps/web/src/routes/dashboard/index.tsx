import ProtectedLayout from '@/components/layout/protected.layout'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useEffect } from 'react'

export const Route = createFileRoute('/dashboard/')({
  component: RouteComponent,
})

function RouteComponent() {
  const navigate = useNavigate()

  // Redirect to system management module (organizations)
  useEffect(() => {
    navigate({ to: '/system/organizations' })
  }, [navigate])

  return (
    <ProtectedLayout>
      <div className="h-screen flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-sm text-gray-600">Đang chuyển hướng...</p>
        </div>
      </div>
    </ProtectedLayout>
  )
}
