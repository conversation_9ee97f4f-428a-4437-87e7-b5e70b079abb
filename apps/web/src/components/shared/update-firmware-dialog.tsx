import {
  FormDialog,
  FormDialogActions,
  FormDialogButton,
} from './form-dialog'

interface EdgeDevice {
  id: string
  name: string
  type: 'FACE_TERMINAL' | 'AI_BOX'
  status: 'ONLINE' | 'OFFLINE' | 'UPDATING'
  ipAddress: string
  firmwareVersion: string
  uptime: string
  lastSignal: string
  syncPercentage: number
}

interface UpdateFirmwareDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedCount: number
  onConfirm: (deviceIds: string[]) => void
  deviceIds: string[]
  singleDevice?: EdgeDevice | null
  clearSelection?: () => void
}

export function UpdateFirmwareDialog({
  open,
  onOpenChange,
  selectedCount,
  onConfirm,
  deviceIds,
  singleDevice,
  clearSelection,
}: UpdateFirmwareDialogProps) {
  const handleConfirm = () => {
    onConfirm(deviceIds)
    onOpenChange(false)
    if (clearSelection && !singleDevice) {
      clearSelection()
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Cập nhật phiên bản"
      footer={
        <FormDialogActions>
          <FormDialogButton variant="secondary" onClick={handleCancel}>
            Hủy bỏ
          </FormDialogButton>
          <FormDialogButton variant="primary" onClick={handleConfirm}>
            Cập nhật
          </FormDialogButton>
        </FormDialogActions>
      }
    >
      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-4">
        <div className="flex flex-col justify-center items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-1 p-3 rounded-lg bg-[#008fd3]/10 border border-[#008fd3]">
          <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 gap-1.5">
            <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-1">
              <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]">
                Phiên bản mới:
              </p>
              <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#1f2329]">1.28</p>
            </div>
          </div>
          <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#1f2329]">
            <span className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#1f2329]">
              Cải thiện tốc độ nhận diện khuôn mặt
            </span>
            <br />
            <span className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#1f2329]">
              Hỗ trợ tính năng nhận diện biển số xe
            </span>
          </p>
        </div>
        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-0.5">
          <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-1">
            <p className="self-stretch flex-grow-0 flex-shrink-0 text-sm text-left text-[#1f2329]">
              {singleDevice ? (
                <>
                  <span className="text-sm text-left text-[#1f2329]">
                    Hệ thống sẽ cập nhật phiên bản thiết bị{" "}
                  </span>
                  <span className="text-sm font-semibold text-left text-[#1f2329]">
                    "{singleDevice.name}"
                  </span>
                  <span className="text-sm text-left text-[#1f2329]">
                    . Thiết bị sẽ tạm thời không thể sử dụng trong một khoảng thời gian.
                  </span>
                </>
              ) : (
                <>
                  <span className="text-sm text-left text-[#1f2329]">
                    Hệ thống sẽ cập nhật phiên bản cho{" "}
                  </span>
                  <span className="text-sm font-semibold text-left text-[#1f2329]">
                    {selectedCount} thiết bị đã chọn
                  </span>
                  <span className="text-sm text-left text-[#1f2329]">
                    . Các thiết bị sẽ tạm thời không thể sử dụng trong một khoảng thời gian.
                  </span>
                </>
              )}
            </p>
          </div>
          <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-1">
            <p className="self-stretch flex-grow-0 flex-shrink-0 text-sm text-left text-[#1f2329]">
              Bạn có chắc chắn muốn tiếp tục thao tác?
            </p>
          </div>
        </div>
      </div>
    </FormDialog>
  )
}
