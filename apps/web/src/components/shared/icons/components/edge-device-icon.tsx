import React from 'react'

interface EdgeDeviceIconProps {
  className?: string
  width?: number
  height?: number
  fillColor?: string
  strokeColor?: string
}

export const EdgeDeviceIcon: React.FC<EdgeDeviceIconProps> = ({
  className = "flex-grow-0 flex-shrink-0 w-[18px] h-[18px] relative",
  width = 18,
  height = 18,
  strokeColor = "#1F2329"
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    preserveAspectRatio="none"
  >
    <path
      d="M15 10.5V7.5C15 4.67157 15 3.25736 14.173 2.37868C13.346 1.5 12.015 1.5 9.35294 1.5L8.64706 1.5C5.98501 1.5 4.65399 1.5 3.82699 2.37868C3 3.25736 3 4.67157 3 7.5L3 10.5C3 13.3284 3 14.7426 3.82699 15.6213C4.65398 16.5 5.98501 16.5 8.64706 16.5H9.35294C12.015 16.5 13.346 16.5 14.173 15.6213C15 14.7426 15 13.3284 15 10.5Z"
      stroke={strokeColor}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M12 13.5H12.0067"
      stroke={strokeColor}
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path 
      d="M3 11.25L15 11.25" 
      stroke={strokeColor} 
      strokeWidth="1.5" 
      strokeLinejoin="round" 
    />
    <path
      d="M6 4.5L7.5 4.5"
      stroke={strokeColor}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 6.75L7.5 6.75"
      stroke={strokeColor}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
