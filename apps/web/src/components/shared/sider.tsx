import * as React from 'react';
import { cn } from '@/utils/cn';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui/sidebar';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Link } from '@tanstack/react-router';
import { ChevronRightIcon, ChevronDownIcon } from 'lucide-react';
import {
  BuildingIcon,
  CustomerIcon,
  ServiceIcon,
  FinanceIcon,
  SupportRequestIcon,
  CollapseNavIcon,
  OrganizationIcon,
  DeviceManagementIcon,
  Settings02Icon,
  MonitoringIcon,
} from '@/components/shared/icons';
import { useModuleSelection } from '@/hooks/use-module-selection';
import { useSidebarNavigation } from '@/hooks/use-route-matching';

import { CCAM_MODULES } from '@/types/ccam-modules';
import { NAVIGATION_COLORS, DEFAULT_MODULE_CONFIG } from '@/shared/constants/navigation';

interface SiderLayoutProps extends React.HTMLAttributes<HTMLDivElement> {}

// Type definitions cho sidebar items
interface SidebarSubItem {
  title: string;
  url: string;
}

interface SidebarItem {
  title: string;
  icon: React.ComponentType<{ className?: string; color?: string }>;
  url?: string;
  children?: SidebarSubItem[];
}

// Cấu hình sidebar items cho từng module
const MODULE_SIDEBAR_ITEMS: Record<string, SidebarItem[]> = {
  // Module mặc định - Quản lý toà nhà
  default: [
    {
      title: 'Toà nhà',
      icon: BuildingIcon,
      children: [
        { title: 'Danh sách toà nhà', url: '/buildings/page' },
        { title: 'Danh sách mặt bằng', url: '/spaces/page' },
      ],
    },
    {
      title: 'Khách hàng',
      url: '/customers/page',
      icon: CustomerIcon,
    },
    {
      title: 'Dịch vụ',
      url: '/services/page',
      icon: ServiceIcon,
    },
    {
      title: 'Sổ quỹ',
      url: '/receipts/page',
      icon: FinanceIcon,
    },
    {
      title: 'Yêu cầu hỗ trợ',
      url: '/tickets/page',
      icon: SupportRequestIcon,
    },
  ],
  // Module Quản trị hệ thống
  systemManagement: [
    {
      title: 'Tổ chức',
      url: '/system/organizations/',
      icon: OrganizationIcon,
    },
    {
      title: 'Quản lý thiết bị',
      icon: DeviceManagementIcon,
      children: [
        { title: 'Danh sách thiết bị', url: '/devices/' },
        { title: 'Cấu hình thiết bị', url: '/devices/config' },
      ],
    },
    {
      title: 'Thiết lập hệ thống',
      icon: Settings02Icon,
      children: [
        { title: 'Cài đặt chung', url: '/settings/general' },
        { title: 'Quản lý quyền', url: '/roles/' },
        { title: 'Đơn vị tổ chức', url: '/units/' },
      ],
    },
    {
      title: 'Theo dõi hệ thống',
      icon: MonitoringIcon,
      children: [
        { title: 'Giám sát hoạt động', url: '/monitoring/activity' },
        { title: 'Báo cáo hệ thống', url: '/monitoring/reports' },
      ],
    },
  ],
  // Module Chấm công
  attendance: [
    {
      title: 'Lịch chấm công',
      url: '/app/attendance/attendance-calendar',
      icon: CustomerIcon,
    },
    {
      title: 'Báo cáo chấm công',
      url: '/app/attendance/reports',
      icon: ServiceIcon,
    },
  ],
};

export function SiderLayout({ className, ...props }: SiderLayoutProps) {
  const { state } = useSidebar();
  const isCollapsed = state === 'collapsed';
  const { selectedModule } = useModuleSelection();
  const { isUrlActive, currentRoute } = useSidebarNavigation();

  // Sử dụng shared constants cho màu sắc
  const COLOR_ACTIVE = NAVIGATION_COLORS.active.text;
  const COLOR_BG_ACTIVE = NAVIGATION_COLORS.active.background;
  const COLOR_INACTIVE = NAVIGATION_COLORS.inactive.text;

  // Lấy items và title dựa trên module được chọn
  const currentModule = selectedModule && CCAM_MODULES[selectedModule] ? CCAM_MODULES[selectedModule] : null;
  const items = MODULE_SIDEBAR_ITEMS[selectedModule as keyof typeof MODULE_SIDEBAR_ITEMS] || MODULE_SIDEBAR_ITEMS.default;
  const moduleTitle = currentModule ? currentModule.title.toUpperCase() : DEFAULT_MODULE_CONFIG.title;

  // Sử dụng React state trực tiếp để debug
  const [openItems, setOpenItems] = React.useState<Record<string, boolean>>(() => {
    try {
      const saved = localStorage.getItem(`sidebar-open-items-${selectedModule || 'default'}`);
      return saved ? JSON.parse(saved) : {};
    } catch {
      return {};
    }
  });

  // Auto-expand logic
  React.useEffect(() => {
    const newOpenItems: Record<string, boolean> = {};
    let hasChanges = false;

    items?.forEach((item) => {
      if (item.children?.length) {
        const shouldBeOpen = item.children.some((child) => isUrlActive(child.url));
        const currentlyOpen = openItems[item.title] || false;

        if (shouldBeOpen && !currentlyOpen) {
          newOpenItems[item.title] = true;
          hasChanges = true;
        }
      }
    });

    if (hasChanges) {
      setOpenItems(prev => {
        const updated = { ...prev, ...newOpenItems };
        // Save to localStorage
        try {
          localStorage.setItem(`sidebar-open-items-${selectedModule || 'default'}`, JSON.stringify(updated));
        } catch (error) {
          console.warn('Failed to save sidebar state:', error);
        }
        return updated;
      });
    }
  }, [items, isUrlActive, currentRoute.pathname, selectedModule, openItems]);

  // Debug logging
  React.useEffect(() => {
    console.log('Sidebar Debug:', {
      pathname: currentRoute.pathname,
      selectedModule,
      currentModule: currentModule?.title,
      itemsLength: items?.length,
      moduleTitle,
      openItems
    });
  }, [currentRoute.pathname, selectedModule, currentModule, items, moduleTitle, openItems]);

  return (
    <Sidebar
      collapsible="icon"
      className={cn(
        'h-[calc(100vh-4rem)] flex flex-col relative [&_[data-sidebar=sidebar]]:rounded-none [&_[data-sidebar=sidebar]]:border-0 [&_[data-sidebar=sidebar]]:shadow-none',
        className,
      )}
      {...props}
    >
      <SidebarContent className="bg-background flex-1">
        <SidebarGroup>
          <p className="p-2 text-[#8F959E]">{moduleTitle}</p>
          <SidebarGroupContent>
            <SidebarMenu>
              {items?.map((item) => {
                const hasChildren = !!item.children?.length;
                const isActiveGroup = hasChildren
                  ? item.children?.some((child) => isUrlActive(child.url))
                  : item.url ? isUrlActive(item.url) : false;

                const isOpen = openItems[item.title] || false;
                const handleToggle = (open: boolean) => {
                  console.log('Toggle clicked:', { itemTitle: item.title, open, isOpen });
                  // Toggle state trực tiếp
                  if (open !== isOpen) {
                    setOpenItems(prev => {
                      const updated = { ...prev, [item.title]: open };
                      // Save to localStorage immediately
                      try {
                        localStorage.setItem(`sidebar-open-items-${selectedModule || 'default'}`, JSON.stringify(updated));
                      } catch (error) {
                        console.warn('Failed to save sidebar state:', error);
                      }
                      return updated;
                    });
                  }
                };

                return hasChildren ? (
                  <Collapsible
                    key={item.title}
                    open={isOpen}
                    onOpenChange={handleToggle}
                  >
                    <SidebarMenuItem>
                      <CollapsibleTrigger asChild>
                        <SidebarMenuButton
                          className={cn(
                            'flex items-center justify-between px-4 py-2 w-full rounded group font-semibold text-[14px]',
                            isActiveGroup
                              ? `${COLOR_ACTIVE} hover:text-${COLOR_ACTIVE}`
                              : `${COLOR_INACTIVE} hover:bg-gray-100`,
                          )}
                        >
                          <div className="flex items-center gap-2">
                            <item.icon
                              className={cn(
                                'w-4 h-4',
                                isActiveGroup
                                  ? 'text-[#008FD3]'
                                  : 'text-[#1F2329]',
                              )}
                              color={isActiveGroup ? '#008FD3' : '#1F2329'}
                            />
                            <span
                              className={cn(
                                isActiveGroup
                                  ? `${COLOR_ACTIVE} hover:text-${COLOR_ACTIVE}`
                                  : `${COLOR_INACTIVE} hover:bg-gray-100`,
                              )}
                            >
                              {item.title}
                            </span>
                          </div>
                          <ChevronDownIcon
                            size={16}
                            className={cn(
                              'transition-transform',
                              isOpen ? 'rotate-180' : 'rotate-0',
                              isActiveGroup
                                ? 'text-[#008FD3]'
                                : 'text-[#1F2329]',
                            )}
                          />
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                    </SidebarMenuItem>

                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {item.children?.map((sub) => {
                          const isSubActive = isUrlActive(sub.url);
                          return (
                            <SidebarMenuSubItem key={sub.title}>
                              <Link
                                to={sub.url}
                                className={cn(
                                  'block px-4 py-2 ml-6 w-full rounded hover:bg-gray-100 font-semibold text-[14px]',
                                  isSubActive &&
                                    `${COLOR_BG_ACTIVE} ${COLOR_ACTIVE}`,
                                )}
                              >
                                {sub.title}
                              </Link>
                            </SidebarMenuSubItem>
                          );
                        })}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </Collapsible>
                ) : (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild>
                      <Link
                        to={item.url}
                        className={cn(
                          'flex items-center gap-2 px-4 py-2 w-full rounded group font-semibold text-[14px]',
                          isActiveGroup
                            ? `${COLOR_BG_ACTIVE} ${COLOR_ACTIVE}`
                            : `${COLOR_INACTIVE} hover:bg-gray-100`,
                        )}
                      >
                        <item.icon
                          className={cn(
                            'w-4 h-4',
                            isActiveGroup ? 'text-[#008FD3]' : 'text-[#1F2329]',
                          )}
                          color={isActiveGroup ? '#008FD3' : '#1F2329'}
                        />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <div className="border-t p-2 flex justify-center">
        <SidebarTrigger className="p-2 rounded-full hover:bg-gray-100 flex items-center gap-2">
          {isCollapsed ? (
            <ChevronRightIcon />
          ) : (
            <>
              <CollapseNavIcon />
              <span className="text-sm font-medium">Ẩn điều hướng</span>
            </>
          )}
        </SidebarTrigger>
      </div>
    </Sidebar>
  );
}

export default SiderLayout;
