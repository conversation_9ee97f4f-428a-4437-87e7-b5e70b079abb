import { ChevronDownLargeIcon, CloseIcon } from '@/components/shared/icons'
import { UserAvatar } from '@/components/shared/user-avatar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover'
import { useEffect, useState } from 'react'
import { createPortal } from 'react-dom'

interface User {
  id: string
  unit_id: string
  member_role_id?: string
  code: string
  name: string
  email?: string
  phone?: string
  dob?: string
  gender?: string
  username: string
  status: 'active' | 'inactive'
  created_by: string
  createdAt: string
  updatedAt: string
  avatar_id?: string
  face_id?: string
  // Populated fields from backend
  tenant?: {
    id: string
    name: string
  }
  unit?: {
    id: string
    name: string
  }
}

interface UserDetailDrawerProps {
  open: boolean
  onClose: () => void
  user: User | null
  onEditUser?: (user: User) => void
  onChangeUnit?: (user: User) => void
  onUpdateAccount?: (user: User) => void
  onToggleStatus?: (user: User) => void
}

export function UserDetailDrawer({
  open,
  onClose,
  user,
  onEditUser,
  onChangeUnit,
  onUpdateAccount,
  onToggleStatus
}: UserDetailDrawerProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (open) {
      setIsVisible(true)
      // Small delay to trigger animation after render
      setTimeout(() => setIsAnimating(true), 10)
    } else {
      setIsAnimating(false)
      // Wait for animation to complete before hiding
      setTimeout(() => setIsVisible(false), 300)
    }
  }, [open])

  const handleClose = () => {
    setIsAnimating(false)
    setTimeout(() => {
      onClose()
    }, 300)
  }

  if (!isVisible || !user) return null

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return '-'
    const d = typeof date === 'string' ? new Date(date) : date
    return d.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatBirthDate = (date: string | undefined) => {
    if (!date) return '-'
    return new Date(date).toLocaleDateString('vi-VN')
  }

  const drawerContent = (
    <>
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-black/20 z-[9998] transition-opacity duration-300 ${
          isAnimating ? 'opacity-100' : 'opacity-0'
        }`}
        onClick={handleClose}
      />

      {/* Drawer */}
      <div
        className={`fixed top-0 right-0 z-[9999] flex flex-col justify-start items-start w-[480px] h-full overflow-hidden bg-white transition-transform duration-300 ease-out ${
          isAnimating ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{
          boxShadow:
            "-10px 0px 36px 10px rgba(31,35,41,0.04), -8px 0px 24px 0 rgba(31,35,41,0.04), -6px 0px 12px -10px rgba(31,35,41,0.06)",
        }}
      >
        {/* Header */}
        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 py-4 bg-white border-b border-[#e9eaf2]">
          <div className="flex justify-between items-center self-stretch flex-grow-0 flex-shrink-0 px-5">
            <div className="flex flex-col justify-center items-start flex-grow-0 flex-shrink-0">
              <p className="text-base font-medium text-[#1f2329]">
                Chi tiết thành viên
              </p>
            </div>
            <button
              onClick={handleClose}
              className="flex justify-center items-center p-1 rounded-md hover:bg-gray-100 transition-colors cursor-pointer"
            >
              <CloseIcon />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex justify-start items-start self-stretch flex-grow overflow-y-auto p-5">
          <div className="flex flex-col justify-start items-start w-full gap-4">
            {/* User Info Header */}
            <div className="flex justify-between items-start self-stretch">
              <div className="flex justify-start items-start gap-2">
                <UserAvatar
                  user={{
                    name: user.name,
                    avatar_id: user.avatar_id,
                    face_id: user.face_id,
                  }}
                  size="lg"
                  className="w-[46px] h-[46px]"
                />
                <div className="flex flex-col justify-start items-start gap-1">
                  <div className="flex justify-start items-center gap-1">
                    <p className="text-base font-medium text-[#081f41]">
                      {user.name}
                    </p>
                    <p className="text-sm font-medium text-[#67718e]">-</p>
                    <p className="text-base font-medium text-[#67718e]">
                      {user.code}
                    </p>
                  </div>
                  <div className="flex justify-start items-center gap-1.5">
                    <div className={`w-2 h-2 rounded-lg ${
                      user.status === 'active' ? 'bg-[#35c724]' : 'bg-gray-400'
                    }`} />
                    <p className="text-sm text-[#1f2329]">
                      {user.status === 'active' ? 'Đang hoạt động' : 'Ngưng hoạt động'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Actions Button */}
              <Popover modal={false}>
                <PopoverTrigger asChild>
                  <button
                    className="flex justify-center items-center gap-2 px-3 py-1.5 rounded-md bg-[#008fd3] cursor-pointer hover:bg-[#008fd3]/90 transition-colors focus:outline-none focus:ring-2 focus:ring-[#008fd3]/50"
                    onClick={(e) => {
                      e.stopPropagation()
                      console.log('Thao tác button clicked')
                    }}
                  >
                    <span className="text-sm font-semibold text-white">Thao tác</span>
                    <ChevronDownLargeIcon strokeColor="white" width={16} height={16} />
                  </button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-56 p-2 z-[10000] bg-white border border-gray-200 shadow-lg"
                  align="end"
                  side="bottom"
                  sideOffset={4}
                  onOpenAutoFocus={(e) => e.preventDefault()}
                >
                  <div className="space-y-1">
                    <button
                      className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation()
                        onEditUser?.(user)
                      }}
                    >
                      Chỉnh sửa
                    </button>
                    <button
                      className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation()
                        onChangeUnit?.(user)
                      }}
                    >
                      Đổi đơn vị
                    </button>
                    <button
                      className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation()
                        onUpdateAccount?.(user)
                      }}
                    >
                      Cập nhật tài khoản
                    </button>
                    <button
                      className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation()
                        onToggleStatus?.(user)
                      }}
                    >
                      {user.status === 'active' ? 'Ngưng hoạt động' : 'Hoạt động'}
                    </button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Tab Header */}
            <div className="flex justify-start items-center self-stretch relative">
              <div className="flex justify-start items-start gap-6">
                <div className="flex flex-col justify-start items-start border-b-[3px] border-[#008fd3]">
                  <div className="py-3">
                    <p className="text-sm font-semibold text-[#008fd3]">
                      Thông tin cơ bản
                    </p>
                  </div>
                </div>
              </div>
            </div>
            {/* User Details */}
            <div className="flex flex-col justify-center items-start self-stretch gap-4 py-2">
              <div className="flex flex-col justify-start items-start self-stretch gap-4">

                <div className="flex justify-start items-start self-stretch gap-4">
                  <div className="w-[100px]">
                    <p className="text-sm text-[#1f2329]/50">Email</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">{user.email}</p>
                  </div>
                </div>

                <div className="flex justify-start items-start self-stretch gap-4">
                  <div className="w-[100px]">
                    <p className="text-sm text-[#1f2329]/50">Tên đăng nhập</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">{user.username}</p>
                  </div>
                </div>

                <div className="flex justify-start items-start self-stretch gap-4">
                  <div className="w-[100px]">
                    <p className="text-sm text-[#1f2329]/50">Số điện thoại</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">{user.phone || '-'}</p>
                  </div>
                </div>

                <div className="flex justify-start items-start self-stretch gap-4">
                  <div className="w-[100px]">
                    <p className="text-sm text-[#1f2329]/50">Tổ chức</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">{user.tenant?.name || '-'}</p>
                  </div>
                </div>

                <div className="flex justify-start items-start self-stretch gap-4">
                  <div className="w-[100px]">
                    <p className="text-sm text-[#1f2329]/50">Đơn vị</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">{user.unit?.name || '-'}</p>
                  </div>
                </div>

                <div className="flex justify-start items-start self-stretch gap-4">
                  <div className="w-[100px]">
                    <p className="text-sm text-[#1f2329]/50">Vai trò</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">{user.member_role_id || '-'}</p>
                  </div>
                </div>

                <div className="flex justify-start items-start self-stretch gap-4">
                  <div className="w-[100px]">
                    <p className="text-sm text-[#1f2329]/50">Ngày sinh</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">{formatBirthDate(user.dob)}</p>
                  </div>
                </div>

                <div className="flex justify-start items-start self-stretch gap-4">
                  <div className="w-[100px]">
                    <p className="text-sm text-[#1f2329]/50">Giới tính</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">{user.gender || '-'}</p>
                  </div>
                </div>

                <div className="flex justify-start items-start self-stretch gap-4">
                  <div className="w-[100px]">
                    <p className="text-sm text-[#1f2329]/50">Thời gian tạo</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">{formatDate(user.createdAt)}</p>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )

  // Render in portal to ensure it's above all other components
  return typeof window !== 'undefined'
    ? createPortal(drawerContent, document.body)
    : null
}
