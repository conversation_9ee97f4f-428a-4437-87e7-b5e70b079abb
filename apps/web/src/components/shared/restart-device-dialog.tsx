import {
  FormDialog,
  FormDialogActions,
  FormDialogButton,
} from './form-dialog'

interface EdgeDevice {
  id: string
  name: string
  type: 'FACE_TERMINAL' | 'AI_BOX'
  status: 'ONLINE' | 'OFFLINE' | 'UPDATING'
  ipAddress: string
  firmwareVersion: string
  uptime: string
  lastSignal: string
  syncPercentage: number
}

interface RestartDeviceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedCount: number
  onConfirm: (deviceIds: string[]) => void
  deviceIds: string[]
  singleDevice?: EdgeDevice | null
  clearSelection?: () => void
}

export function RestartDeviceDialog({
  open,
  onOpenChange,
  selectedCount,
  onConfirm,
  deviceIds,
  singleDevice,
  clearSelection,
}: RestartDeviceDialogProps) {
  const handleConfirm = () => {
    onConfirm(deviceIds)
    onOpenChange(false)
    if (clearSelection && !singleDevice) {
      clearSelection()
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Khởi động lại thiết bị"
      footer={
        <FormDialogActions>
          <FormDialogButton variant="secondary" onClick={handleCancel}>
            Hủy bỏ
          </FormDialogButton>
          <FormDialogButton variant="primary" onClick={handleConfirm}>
            Khởi động lại
          </FormDialogButton>
        </FormDialogActions>
      }
    >
      <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-0.5">
        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-1">
          <p className="self-stretch flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]">
            {singleDevice ? (
              <>
                <span className="text-sm text-left text-[#23262f]">
                  Hệ thống sẽ khởi động lại thiết bị{" "}
                </span>
                <span className="text-sm font-semibold text-left text-[#23262f]">
                  "{singleDevice.name}"
                </span>
                <span className="text-sm text-left text-[#23262f]">
                  . Thiết bị sẽ tạm thời không thể sử dụng trong một khoảng thời gian ngắn
                </span>
              </>
            ) : (
              <>
                <span className="text-sm text-left text-[#23262f]">
                  Hệ thống sẽ khởi động lại{" "}
                </span>
                <span className="text-sm font-semibold text-left text-[#23262f]">
                  {selectedCount} thiết bị đã chọn
                </span>
                <span className="text-sm text-left text-[#23262f]">
                  . Các thiết bị sẽ tạm thời không thể sử dụng trong một khoảng thời gian ngắn
                </span>
              </>
            )}
          </p>
        </div>
        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-1">
          <p className="self-stretch flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]">
            Bạn có chắc chắn muốn tiếp tục thao tác?
          </p>
        </div>
      </div>
    </FormDialog>
  )
}
