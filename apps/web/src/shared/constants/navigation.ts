/**
 * Navigation Constants
 * Centralized configuration for navigation, routing, and module patterns
 */

import type { ModuleKey } from '@/types/ccam-modules'

/**
 * Module route patterns for automatic module detection
 * Used by navigation hooks to determine which module is currently active
 */
export const MODULE_ROUTE_PATTERNS: Record<ModuleKey | 'default', string[]> = {
  systemManagement: [
    '/system/organizations/',
    '/devices',
    '/settings',
    '/roles',
    '/units',
    '/monitoring'
  ],
  attendance: [
    '/app/attendance'
  ],
  default: [
    '/buildings',
    '/customers',
    '/services',
    '/receipts',
    '/tickets'
  ]
} as const

/**
 * Route matching configuration
 */
export const ROUTE_MATCHING_CONFIG = {
  /**
   * Whether to enable exact matching for routes
   */
  exactMatch: true,

  /**
   * Whether to handle trailing slash variations
   */
  handleTrailingSlash: true,

  /**
   * Whether to support nested route matching
   */
  supportNestedRoutes: true,

  /**
   * Patterns to exclude from nested matching to avoid false positives
   */
  excludePatterns: [
    // Avoid '/device' matching '/devices'
    { pattern: '/device', exclude: '/devices' }
  ]
} as const

/**
 * Sidebar navigation configuration
 */
export const SIDEBAR_CONFIG = {
  /**
   * LocalStorage key prefix for sidebar state
   */
  localStoragePrefix: 'sidebar-open-items',

  /**
   * Default expanded state for menu groups
   */
  defaultExpanded: false,

  /**
   * Whether to persist sidebar state across sessions
   */
  persistState: true,

  /**
   * Debounce time for localStorage saves (ms)
   */
  saveDebounceMs: 100
} as const

/**
 * Navigation colors and styling
 */
export const NAVIGATION_COLORS = {
  active: {
    text: 'text-[#008FD3]',
    background: 'bg-[#008FD3]/10',
    icon: '#008FD3'
  },
  inactive: {
    text: 'text-[#1F2329]',
    background: 'hover:bg-gray-100',
    icon: '#1F2329'
  },
  moduleTitle: 'text-[#8F959E]'
} as const

/**
 * Default module configuration
 */
export const DEFAULT_MODULE_CONFIG = {
  key: 'systemManagement' as const,
  title: 'QUẢN TRỊ HỆ THỐNG',
  fallbackRoute: '/system/organizations/'
} as const

/**
 * Navigation event types for analytics or debugging
 */
export const NAVIGATION_EVENTS = {
  MODULE_CHANGED: 'navigation:module_changed',
  ROUTE_MATCHED: 'navigation:route_matched',
  SIDEBAR_TOGGLED: 'navigation:sidebar_toggled',
  MENU_EXPANDED: 'navigation:menu_expanded'
} as const
