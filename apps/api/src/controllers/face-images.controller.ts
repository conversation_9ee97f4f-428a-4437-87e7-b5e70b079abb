import {
  Authorized,
  Controller,
  ControllerBase,
  HttpContext,
  HttpDele<PERSON>,
  HttpGet,
  HttpPost,
  HttpPut,
  Inject,
  Param,
  Query
} from '@c-cam/core';
import { Context } from 'hono';
import FaceImagesService from '../services/FaceImagesService';
import {
  createAuthorizedOptions,
  getClassPolicy,
  methodRef,
} from '../utils/controller-policies.js';

@Controller('/api/face-images')
@Authorized({ policies: [getClassPolicy(FaceImagesController.name)] })
export class FaceImagesController extends ControllerBase {
  constructor(@Inject(FaceImagesService) private faceImagesService: FaceImagesService) {
    super();
  }

  /**
   * Get all face images
   */
  @HttpGet('/')
  @Authorized(
    createAuthorizedOptions(
      FaceImagesController.name,
      methodRef<FaceImagesController>('getFaceImages'),
    ),
  )
  async getFaceImages(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    this.getCurrentUser(c.req);

    const faceImages = await this.faceImagesService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { faceImages });
  }

  /**
   * Get a face image by ID
   */
  @HttpGet('/:id')
  @Authorized(
    createAuthorizedOptions(
      FaceImagesController.name,
      methodRef<FaceImagesController>('getFaceImageById'),
    ),
  )
  async getFaceImageById(@HttpContext() c: Context, @Param('id') id: string): Promise<Response> {
    this.getCurrentUser(c.req);
    this.validateIf(!id, 'Face image ID is required');

    const faceImage = await this.faceImagesService.findById(id);
    this.notFoundIf(!faceImage, 'Face image not found');

    return this.success(c, { faceImage });
  }

  /**
   * Create a new face image
   */
  @HttpPost('/')
  @Authorized(
    createAuthorizedOptions(
      FaceImagesController.name,
      methodRef<FaceImagesController>('createFaceImage'),
    ),
  )
  async createFaceImage(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const imageData = await c.req.json();

      // Add the creator ID
      imageData.created_by = userId;

      // Validate required fields
      if (!imageData.user_id || !imageData.image_url) {
        return c.json({ error: 'User ID and Image URL are required' }, 400);
      }

      const faceImage = await this.faceImagesService.createFaceImage(imageData);
      return c.json({ faceImage }, 201);
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Update a face image
   */
  @HttpPut('/:id')
  @Authorized(
    createAuthorizedOptions(
      FaceImagesController.name,
      methodRef<FaceImagesController>('updateFaceImage'),
    ),
  )
  async updateFaceImage(@HttpContext() c: Context): Promise<Response> {
    try {
      const user = await this.getCurrentUser(c.req);
      const userId = user?.id;
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();

      if (!id) {
        return c.json({ error: 'Face image ID is required' }, 400);
      }
      const imageData = await c.req.json();

      const success = await this.faceImagesService.updateFaceImage(id, imageData);

      if (!success) {
        return c.json({ error: 'Failed to update face image' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Delete a face image
   */
  @HttpDelete('/:id')
  @Authorized(
    createAuthorizedOptions(
      FaceImagesController.name,
      methodRef<FaceImagesController>('deleteFaceImage'),
    ),
  )
  async deleteFaceImage(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();

      if (!id) {
        return c.json({ error: 'Face image ID is required' }, 400);
      }
      const success = await this.faceImagesService.delete(id);

      if (!success) {
        return c.json({ error: 'Failed to delete face image' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face images by user ID
   */
  @HttpGet('/user/:userId')
  async getFaceImagesByUserId(@HttpContext() c: Context): Promise<Response> {
    try {
      const authUserId = c.get('userId');
      if (!authUserId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { userId } = c.req.param();
      if (!userId) {
        return c.json({ error: 'User ID is required' }, 400);
      }
      const faceImages = await this.faceImagesService.findByUserId(userId);

      return c.json({ faceImages });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face images by image angle
   */
  @HttpGet('/angle/:imageAngle')
  async getFaceImagesByImageAngle(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { imageAngle } = c.req.param();
      if (!imageAngle) {
        return c.json({ error: 'Image angle is required' }, 400);
      }
      const faceImages = await this.faceImagesService.findByImageAngle(imageAngle);

      return c.json({ faceImages });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face images by date range
   */
  @HttpGet('/date-range/:startDate/:endDate')
  async getFaceImagesByDateRange(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { startDate, endDate } = c.req.param();

      if (!startDate || !endDate) {
        return c.json({ error: 'Start and end dates are required' }, 400);
      }
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return c.json({ error: 'Invalid date format' }, 400);
      }

      const faceImages = await this.faceImagesService.findByDateRange(start, end);

      return c.json({ faceImages });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face images by user ID and image angle
   */
  @HttpGet('/user/:userId/angle/:imageAngle')
  async getFaceImagesByUserIdAndImageAngle(@HttpContext() c: Context): Promise<Response> {
    try {
      const authUserId = c.get('userId');
      if (!authUserId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { userId, imageAngle } = c.req.param();

      if (!userId || !imageAngle) {
        return c.json({ error: 'User ID and image angle are required' }, 400);
      }
      const faceImages = await this.faceImagesService.findByUserIdAndImageAngle(userId, imageAngle);

      return c.json({ faceImages });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Delete face images by user ID
   */
  @HttpDelete('/user/:userId')
  async deleteFaceImagesByUserId(@HttpContext() c: Context): Promise<Response> {
    try {
      const authUserId = c.get('userId');
      if (!authUserId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { userId } = c.req.param();

      if (!userId) {
        return c.json({ error: 'User ID is required' }, 400);
      }
      const success = await this.faceImagesService.deleteByUserId(userId);

      if (!success) {
        return c.json({ error: 'Failed to delete face images' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Get the latest front-facing image for a user
   */
  @HttpGet('/user/:userId/latest-front')
  async getLatestFrontImage(@HttpContext() c: Context): Promise<Response> {
    try {
      const authUserId = c.get('userId');
      if (!authUserId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { userId } = c.req.param();

      if (!userId) {
        return c.json({ error: 'User ID is required' }, 400);
      }
      const faceImage = await this.faceImagesService.getLatestFrontImage(userId);

      if (!faceImage) {
        return c.json({ error: 'No front-facing image found for the user' }, 404);
      }

      return c.json({ faceImage });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }
}
