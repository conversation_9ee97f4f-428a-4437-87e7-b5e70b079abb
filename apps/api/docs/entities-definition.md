# C-CAM System Entities Definition

This document defines all entities used in the C-CAM (Camera-based Attendance Management) system. Each entity represents a core data structure with its attributes, relationships, and business logic.

## Table of Contents

1. [Core Entities](#core-entities)
   - [Users](#users)
   - [Roles & Permissions](#roles--permissions)
   - [Organizational Structure](#organizational-structure)
2. [Device Management](#device-management)
   - [Cameras](#cameras)
   - [Edge Devices](#edge-devices)
3. [File Storage](#file-storage)
   - [Files & Buckets](#files--buckets)
4. [Attendance Management](#attendance-management)
   - [Shifts & Schedules](#shifts--schedules)
   - [Face Recognition](#face-recognition)
   - [Attendance Tracking](#attendance-tracking)
5. [Logging & Auditing](#logging--auditing)
   - [Activity Logs](#activity-logs)
   - [Audit Trails](#audit-trails)

---

## Core Entities

### Users

**Entity**: `UsersAttributes`
**Description**: Core user entity representing individuals in the system with authentication and profile information.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `unit_id` | string | ✅ | Reference to organizational unit |
| `face_id` | string | ❌ | Reference to face recognition data |
| `avatar_id` | string | ❌ | Reference to user avatar file |
| `member_role_id` | string | ❌ | Reference to member role |
| `code` | string | ✅ | Unique user code/employee ID |
| `name` | string | ✅ | Full name |
| `email` | string | ❌ | Email address |
| `phone` | string | ❌ | Phone number |
| `dob` | Date | ❌ | Date of birth |
| `gender` | UserGender | ❌ | Gender (male/female/other) |
| `username` | string | ✅ | Login username |
| `password` | string | ✅ | Encrypted password |
| `status` | UserStatus | ✅ | Account status |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |
| `updated_at` | Date | ❌ | Last update timestamp |

#### Enums

**UserStatus**:
- `ACTIVE`: Active user account
- `INACTIVE`: Temporarily disabled
- `SUSPENDED`: Suspended due to violations
- `PENDING`: Awaiting activation

**UserGender**:
- `MALE`: Male
- `FEMALE`: Female
- `OTHER`: Other/Non-binary

#### Relationships

- **Unit**: `unit_id` → `UnitAttributes.id`
- **Member Role**: `member_role_id` → `MemberRoleAttributes.id`
- **Face Images**: `id` ← `FaceImagesAttributes.user_id`
- **Avatar File**: `avatar_id` → `FileAttributes.id`

#### Response Types

**UserResponse**: Excludes sensitive data (password) and includes computed fields:
- `full_name`, `first_name`, `last_name`
- Populated `tenant` and `unit` objects

**UserListResponse**: Paginated list of users
**SingleUserResponse**: Single user with full details

---

## Roles & Permissions

### Roles

**Entity**: `RoleAttributes`
**Description**: Defines roles that can be assigned to users for access control.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `member_role_id` | string | ✅ | Reference to member role |
| `permission_id` | string | ✅ | Reference to permission |
| `name` | string | ✅ | Role name |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |

#### Relationships

- **Member Role**: `member_role_id` → `MemberRoleAttributes.id`
- **Permission**: `permission_id` → `PermissionAttributes.id`

### Permissions

**Entity**: `PermissionAttributes`
**Description**: Defines specific permissions for system features and actions.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `role_id` | string | ✅ | Reference to role |
| `module` | string | ✅ | System module (e.g., 'users', 'cameras') |
| `feature` | string | ✅ | Feature within module |
| `action` | string | ✅ | Specific action (create, read, update, delete) |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |

#### Relationships

- **Role**: `role_id` → `RoleAttributes.id`

### Member Roles

**Entity**: `MemberRoleAttributes`
**Description**: Junction entity linking users to roles.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `role_id` | string | ✅ | Reference to role |
| `user_id` | string | ✅ | Reference to user |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |

#### Relationships

- **Role**: `role_id` → `RoleAttributes.id`
- **User**: `user_id` → `UsersAttributes.id`

---

## Organizational Structure

### Units

**Entity**: `UnitAttributes`
**Description**: Represents organizational units (departments, teams, branches).

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `organization_id` | string | ✅ | Reference to organization/tenant |
| `user_id` | string | ✅ | Unit manager user ID |
| `parent_unit_id` | string | ✅ | Parent unit for hierarchy |
| `name` | string | ✅ | Unit name |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |

#### Relationships

- **Organization**: `organization_id` → `TenantAttributes.id`
- **Manager**: `user_id` → `UsersAttributes.id`
- **Parent Unit**: `parent_unit_id` → `UnitAttributes.id`
- **Child Units**: `id` ← `UnitAttributes.parent_unit_id`
- **Users**: `id` ← `UsersAttributes.unit_id`

### Tenants

**Entity**: `TenantAttributes`
**Description**: Represents organizations or companies using the system.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `name` | string | ✅ | Organization name |
| `address` | string | ❌ | Physical address |
| `unit_id` | string | ❌ | Reference to root unit |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |

#### Relationships

- **Root Unit**: `unit_id` → `UnitAttributes.id`
- **Units**: `id` ← `UnitAttributes.organization_id`

---

## Device Management

### Cameras

**Entity**: `CameraAttributes`
**Description**: Represents physical cameras used for face recognition and monitoring.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `name` | string | ✅ | Camera name/label |
| `ip_address` | string | ✅ | Camera IP address |
| `rtsp_url` | string | ✅ | RTSP stream URL |
| `preview_image_url` | string | ✅ | Preview image URL |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |

#### Relationships

- **Edge Devices**: `id` ← `EdgeDeviceAttributes.camera_id`
- **Face Recognition Logs**: `id` ← `FaceRecognitionLogsAttributes.camera_id`

### Edge Devices

**Entity**: `EdgeDeviceAttributes`
**Description**: Edge computing devices that process camera feeds for face recognition.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `camera_id` | string | ✅ | Reference to camera |
| `name` | string | ✅ | Device name |
| `type` | string | ✅ | Device type |
| `ip_address` | string | ✅ | Device IP address |
| `mac_address` | string | ✅ | MAC address |
| `firmware_version` | string | ✅ | Firmware version |
| `is_attendance_device` | boolean | ✅ | Whether used for attendance |
| `attendance_mode` | string | ✅ | Attendance tracking mode |
| `preferred_stream_mode` | string | ✅ | Preferred streaming mode |
| `status` | string | ✅ | Device status |
| `created_at` | Date | ✅ | Creation timestamp |
| `created_by` | string | ✅ | Creator user ID |

#### Relationships

- **Camera**: `camera_id` → `CameraAttributes.id`
- **Device Info**: `id` ← `EdgeDeviceInfoAttributes.edge_device_id`
- **Device Logs**: `id` ← `EdgeDeviceLogsAttributes.edge_device_id`
- **Face Recognition Logs**: `id` ← `FaceRecognitionLogsAttributes.edge_device_id`

### Edge Device Info

**Entity**: `EdgeDeviceInfoAttributes`
**Description**: Real-time system information and metrics for edge devices.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `edge_device_id` | string | ✅ | Reference to edge device |
| `ram_usage` | number | ✅ | RAM usage percentage |
| `cpu_usage` | number | ✅ | CPU usage percentage |
| `disk_usage` | number | ✅ | Disk usage in bytes |
| `total_disk` | number | ✅ | Total disk space in bytes |
| `last_signal` | Date | ✅ | Last communication timestamp |
| `uptime_hours` | number | ✅ | Device uptime in hours |
| `sync_percent` | number | ✅ | Data synchronization percentage |
| `created_at` | Date | ✅ | Creation timestamp |
| `created_by` | string | ✅ | Creator user ID |

#### Relationships

- **Edge Device**: `edge_device_id` → `EdgeDeviceAttributes.id`

### Edge Device Logs

**Entity**: `EdgeDeviceLogsAttributes`
**Description**: Event logs from edge devices for monitoring and troubleshooting.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `edge_device_id` | string | ✅ | Reference to edge device |
| `event_type` | string | ✅ | Type of event |
| `description` | string | ✅ | Event description |
| `created_at` | Date | ✅ | Event timestamp |
| `created_by` | string | ✅ | Creator user ID |

#### Relationships

- **Edge Device**: `edge_device_id` → `EdgeDeviceAttributes.id`

---

## File Storage

### Files

**Entity**: `FileAttributes`
**Description**: Represents files stored in the system (images, documents, etc.).

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `bucket_id` | string | ✅ | Reference to storage bucket |
| `original_name` | string | ✅ | Original filename |
| `stored_name` | string | ✅ | Stored filename |
| `file_path` | string | ✅ | File path in storage |
| `file_size` | number | ✅ | File size in bytes |
| `mime_type` | string | ✅ | MIME type |
| `file_extension` | string | ✅ | File extension |
| `checksum` | string | ❌ | File checksum for integrity |
| `metadata` | Record<string, any> | ❌ | Additional metadata |
| `tags` | string[] | ❌ | File tags |
| `is_public` | boolean | ✅ | Whether file is publicly accessible |
| `download_count` | number | ✅ | Number of downloads |
| `last_accessed_at` | Date | ❌ | Last access timestamp |
| `expires_at` | Date | ❌ | Expiration timestamp |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |
| `updated_at` | Date | ❌ | Last update timestamp |

#### Relationships

- **Bucket**: `bucket_id` → `BucketAttributes.id`
- **User Avatars**: `id` ← `UsersAttributes.avatar_id`

#### Request/Response Types

**FileUploadRequest**: Data for uploading files
**FileUpdateRequest**: Data for updating file metadata
**FileQueryOptions**: Query parameters for file search
**FileDownloadResponse**: Download URL and metadata
**FileUploadResponse**: Upload result and metadata
**PresignedUrlRequest/Response**: For secure file operations

### Buckets

**Entity**: `BucketAttributes`
**Description**: Storage containers for organizing files with access control and policies.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `name` | string | ✅ | Bucket name |
| `description` | string | ❌ | Bucket description |
| `is_public` | boolean | ✅ | Whether bucket is publicly accessible |
| `region` | string | ❌ | Storage region |
| `versioning_enabled` | boolean | ✅ | Whether versioning is enabled |
| `encryption_enabled` | boolean | ✅ | Whether encryption is enabled |
| `lifecycle_policy` | Record<string, any> | ❌ | Lifecycle management policy |
| `cors_policy` | Record<string, any> | ❌ | CORS policy |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |
| `updated_at` | Date | ❌ | Last update timestamp |

#### Relationships

- **Files**: `id` ← `FileAttributes.bucket_id`

#### Request/Response Types

**CreateBucketRequest**: Data for creating buckets
**UpdateBucketRequest**: Data for updating bucket settings
**BucketQueryOptions**: Query parameters for bucket search

---

## Attendance Management

### Shifts

**Entity**: `ShiftAttributes`
**Description**: Defines work shifts with timing and compensation rules.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `name` | string | ✅ | Shift name |
| `shift_type` | string | ✅ | Type of shift |
| `work_coefficient` | number | ✅ | Work coefficient for calculations |

#### Relationships

- **Shift Details**: `id` ← `ShiftDetailAttributes.shift_id`
- **Daily Attendance**: `id` ← `DailyAttendanceSummariesAttributes.shift_id`

### Shift Details

**Entity**: `ShiftDetailAttributes`
**Description**: Detailed timing rules and policies for shifts.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `shift_id` | string | ✅ | Reference to shift |
| `is_overnight` | boolean | ✅ | Whether shift spans midnight |
| `check_in_start_time` | string | ✅ | Check-in start time |
| `late_threshold_time` | string | ✅ | Late arrival threshold |
| `half_day_missed_start_time` | string | ✅ | Half-day missed threshold |
| `break_time` | string | ✅ | Break duration |
| `check_out_start_time` | string | ✅ | Check-out start time |
| `early_leave_threshold_time` | string | ✅ | Early leave threshold |
| `half_day_missed_end_time` | string | ✅ | Half-day missed end threshold |
| `total_working_hours` | number | ✅ | Total working hours |
| `check_in_required` | boolean | ✅ | Whether check-in is required |
| `flex_late_threshold_time` | string | ✅ | Flexible late threshold |
| `flex_half_day_missed_time` | string | ✅ | Flexible half-day threshold |

#### Relationships

- **Shift**: `shift_id` → `ShiftAttributes.id`

### Face Images

**Entity**: `FaceImagesAttributes`
**Description**: Stores face images for users for recognition training.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `user_id` | string | ✅ | Reference to user |
| `image_url` | string | ✅ | Face image URL |
| `image_angle` | string | ✅ | Image angle/pose |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |

#### Relationships

- **User**: `user_id` → `UsersAttributes.id`

### Face Recognition Logs

**Entity**: `FaceRecognitionLogsAttributes`
**Description**: Logs of face recognition events for attendance tracking.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `edge_device_id` | string | ✅ | Reference to edge device |
| `user_id` | string | ✅ | Reference to recognized user |
| `device_id` | string | ✅ | Device identifier |
| `camera_id` | string | ✅ | Reference to camera |
| `recognized_at` | Date | ✅ | Recognition timestamp |
| `image_url` | string | ✅ | Recognition image URL |
| `similarity_percent` | number | ✅ | Recognition confidence |
| `status` | string | ✅ | Recognition status |
| `created_at` | Date | ✅ | Creation timestamp |
| `created_by` | string | ✅ | Creator user ID |

#### Relationships

- **Edge Device**: `edge_device_id` → `EdgeDeviceAttributes.id`
- **User**: `user_id` → `UsersAttributes.id`
- **Camera**: `camera_id` → `CameraAttributes.id`

### Daily Attendance Summaries

**Entity**: `DailyAttendanceSummariesAttributes`
**Description**: Daily attendance records with calculated metrics and status.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `user_id` | string | ✅ | Reference to user |
| `shift_id` | string | ✅ | Reference to shift |
| `holiday_id` | string | ✅ | Reference to holiday (if applicable) |
| `work_date` | Date | ✅ | Work date |
| `is_late` | boolean | ✅ | Whether user was late |
| `is_early_leave` | boolean | ✅ | Whether user left early |
| `checkin_time` | Date | ✅ | Check-in timestamp |
| `checkout_time` | Date | ✅ | Check-out timestamp |
| `late_minutes` | number | ✅ | Minutes late |
| `early_leave_minutes` | number | ✅ | Minutes of early leave |
| `expected_work_minutes` | number | ✅ | Expected work duration |
| `total_work_minutes` | number | ✅ | Actual work duration |
| `note` | string | ✅ | Additional notes |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |

#### Relationships

- **User**: `user_id` → `UsersAttributes.id`
- **Shift**: `shift_id` → `ShiftAttributes.id`

---

## Logging & Auditing

### Activity Logs

**Entity**: `ActivityLogAttributes`
**Description**: General activity logs for user actions and system events.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `user_id` | string | ✅ | Reference to user |
| `action` | string | ✅ | Action performed |
| `resource_type` | string | ✅ | Type of resource affected |
| `resource_id` | string | ❌ | ID of affected resource |
| `description` | string | ✅ | Action description |
| `metadata` | Record<string, any> | ❌ | Additional metadata |
| `ip_address` | string | ❌ | User IP address |
| `user_agent` | string | ❌ | User agent string |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |

#### Relationships

- **User**: `user_id` → `UsersAttributes.id`

#### Query Options

**ActivityLogQueryOptions**: Query parameters for filtering activity logs:
- `action`, `resourceType`, `startDate`, `endDate`
- Standard pagination and sorting options

### Audit Trail Logs

**Entity**: `AuditTrailLogsAttributes`
**Description**: Audit trail logs for compliance and security monitoring.

#### Attributes

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique identifier |
| `user_id` | string | ✅ | Reference to user |
| `module` | string | ✅ | System module |
| `action` | string | ✅ | Action performed |
| `description` | string | ✅ | Action description |
| `created_by` | string | ✅ | Creator user ID |
| `created_at` | Date | ✅ | Creation timestamp |

#### Relationships

- **User**: `user_id` → `UsersAttributes.id`

---

## Entity Relationships Overview

### Core Relationship Patterns

1. **User-Centric Relationships**:
   - Users belong to Units (organizational hierarchy)
   - Users have Member Roles (role assignments)
   - Users have Face Images (for recognition)
   - Users have Activity Logs (audit trail)

2. **Role-Based Access Control**:
   - Roles have Permissions (granular access)
   - Member Roles link Users to Roles
   - Permissions define Module/Feature/Action access

3. **Device Management**:
   - Cameras connect to Edge Devices
   - Edge Devices have Info and Logs
   - Face Recognition Logs link Devices to Users

4. **File Storage**:
   - Files belong to Buckets
   - Files can be User Avatars
   - Files have metadata and access control

5. **Attendance Tracking**:
   - Users have Daily Attendance records
   - Attendance links to Shifts
   - Face Recognition triggers Attendance

### Data Flow Examples

1. **User Authentication**:
   ```
   User → Member Role → Role → Permission → Access Control
   ```

2. **Face Recognition Attendance**:
   ```
   Camera → Edge Device → Face Recognition Log → User → Daily Attendance
   ```

3. **File Access**:
   ```
   User → Permission Check → Bucket → File → Download/Upload
   ```

4. **Organizational Hierarchy**:
   ```
   Tenant → Unit (Parent) → Unit (Child) → Users
   ```

---

## Best Practices

### Entity Design Principles

1. **Consistency**: All entities have `id`, `created_by`, `created_at`
2. **Auditability**: Track who created/modified what and when
3. **Relationships**: Use foreign keys for data integrity
4. **Flexibility**: Use metadata fields for extensibility
5. **Security**: Separate sensitive data (passwords) from responses

### Naming Conventions

1. **Entities**: PascalCase with "Attributes" suffix
2. **Fields**: snake_case for database compatibility
3. **Enums**: UPPER_CASE values
4. **Types**: Descriptive names with purpose suffix

### Performance Considerations

1. **Indexing**: Index foreign keys and frequently queried fields
2. **Pagination**: Use limit/skip for large datasets
3. **Caching**: Cache frequently accessed reference data
4. **Relationships**: Use appropriate loading strategies (eager/lazy)

This comprehensive entity definition serves as the foundation for the C-CAM system's data architecture, ensuring consistency, scalability, and maintainability across all system components.
