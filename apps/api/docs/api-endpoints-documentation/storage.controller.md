# Storage API Documentation

Base API path: `/api/storage`

This API provides endpoints for managing storage buckets and files in the system. All endpoints require authorization.

## Bucket Endpoints

### Create Bucket

**POST** `/api/storage/buckets`

Create a new storage bucket.

**Request Body**
```json
{
  "name": "my-bucket",     // required - Name of the bucket
  "description": "My storage bucket for documents",  // optional - Description of the bucket
  "is_public": false,      // optional - Whether the bucket is publicly accessible
  "retention_days": 90,   // optional - Number of days to retain files
  "tags": ["documents", "reports"]  // optional - Tags for categorization
}
```

**Response**
```json
{
  "success": true,
  "message": "Bucket created successfully",
  "data": {
    "id": "string",
    "name": "my-bucket",
    "description": "My storage bucket for documents",
    "is_public": false,
    "retention_days": 90,
    "created_by": "user-id",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z",
    "tags": ["documents", "reports"]
  }
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing or validation fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `StorageController.createBucket`

---

### Get Bucket By ID

**GET** `/api/storage/buckets/:id`

Retrieve a specific bucket by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the bucket

**Response**
```json
{
  "success": true,
  "data": {
    "id": "string",
    "name": "my-bucket",
    "description": "My storage bucket for documents",
    "is_public": false,
    "retention_days": 90,
    "created_by": "user-id",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z",
    "tags": ["documents", "reports"]
  }
}
```

**Error Responses**
- `404 Not Found`: When bucket with the provided ID doesn't exist
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `StorageController.getBucketById`

---

### List Buckets

**GET** `/api/storage/buckets`

Retrieve a list of all buckets based on optional query parameters.

**Query Parameters**
- `limit` (optional): Maximum number of buckets to return
- `skip` (optional): Number of buckets to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)
- `is_public` (optional): Filter by public/private status (true/false)
- `created_by` (optional): Filter by creator user ID
- `search` (optional): Search term for bucket name or description

**Response**
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "my-bucket",
      "description": "My storage bucket for documents",
      "is_public": false,
      "retention_days": 90,
      "created_by": "user-id",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "tags": ["documents", "reports"]
    }
  ],
  "meta": {
    "count": 1,
    "limit": 50,
    "skip": 0
  }
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `StorageController.listBuckets`

---

### Update Bucket

**PUT** `/api/storage/buckets/:id`

Update an existing bucket.

**Path Parameters**
- `id` (required): The unique identifier of the bucket to update

**Request Body**
```json
{
  "name": "updated-bucket-name",     // optional - Updated name of the bucket
  "description": "Updated description",  // optional - Updated description
  "is_public": true,                   // optional - Updated public access status
  "retention_days": 120,              // optional - Updated retention period
  "tags": ["updated", "documents"]    // optional - Updated tags
}
```

**Response**
```json
{
  "success": true,
  "message": "Bucket updated successfully",
  "data": {
    "id": "string",
    "name": "updated-bucket-name",
    "description": "Updated description",
    "is_public": true,
    "retention_days": 120,
    "created_by": "user-id",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T01:00:00.000Z",
    "tags": ["updated", "documents"]
  }
}
```

**Error Responses**
- `400 Bad Request`: When validation fails
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When bucket with the provided ID doesn't exist

**Authorization**
Needs policy for `StorageController.updateBucket`

---

### Delete Bucket

**DELETE** `/api/storage/buckets/:id`

Delete a bucket by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the bucket to delete

**Response**
```json
{
  "success": true,
  "message": "Bucket deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When deletion fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `StorageController.deleteBucket`

---

### Generate Upload URL

**POST** `/api/storage/buckets/:bucketId/upload-url`

Generate a presigned URL for uploading a file to a bucket.

**Path Parameters**
- `bucketId` (required): The ID of the bucket to upload to

**Request Body**
```json
{
  "fileName": "document.pdf",  // required - Name of the file to upload
  "expiresIn": 3600           // optional - URL expiration time in seconds, defaults to 3600 (1 hour)
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "uploadUrl": "https://storage-url.com/presigned-url",
    "objectName": "1633046400000-document.pdf",
    "bucketName": "my-bucket",
    "expiresIn": 3600
  }
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When bucket with the provided ID doesn't exist
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `StorageController.generateUploadUrl`

---

### Generate Download URL

**POST** `/api/storage/buckets/:bucketId/download-url`

Generate a presigned URL for downloading a file from a bucket.

**Path Parameters**
- `bucketId` (required): The ID of the bucket containing the file

**Request Body**
```json
{
  "objectName": "1633046400000-document.pdf",  // required - Object name in the storage
  "expiresIn": 3600                           // optional - URL expiration time in seconds, defaults to 3600 (1 hour)
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "downloadUrl": "https://storage-url.com/presigned-download-url",
    "objectName": "1633046400000-document.pdf",
    "bucketName": "my-bucket",
    "expiresIn": 3600
  }
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When bucket with the provided ID doesn't exist
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `StorageController.generateDownloadUrl`

---

## File Endpoints

### Stream File

**GET** `/api/storage/files/:fileId/stream`

Stream a file by its ID.

**Path Parameters**
- `fileId` (required): The unique identifier of the file to stream

**Response**
The file stream with appropriate headers:
- Content-Type: The MIME type of the file
- Content-Length: The size of the file in bytes
- Content-Disposition: Inline with the original filename
- Cache-Control: Public with 1-year max age
- ETag: The file's checksum if available

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When file with the provided ID doesn't exist
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `StorageController.streamFile`