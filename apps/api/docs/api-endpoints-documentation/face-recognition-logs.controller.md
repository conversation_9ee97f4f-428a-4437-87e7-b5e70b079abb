# Face Recognition Logs API Documentation

Base API path: `/api/face-recognition-logs`

This API provides endpoints for managing face recognition logs in the system. All endpoints require authorization.

## Endpoints

### Get All Face Recognition Logs

**GET** `/api/face-recognition-logs/`

Retrieve a list of all face recognition logs based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of logs to return
- `skip` (optional): Number of logs to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "logs": [
    {
      "id": "string",
      "camera_id": "string",
      "edge_device_id": "string",
      "device_id": "string",
      "user_id": "string",
      "status": "string",
      "similarity_percent": 95.5,
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `FaceRecognitionLogsController.getFaceRecognitionLogs`

---

### Get Face Recognition Log By ID

**GET** `/api/face-recognition-logs/:id`

Retrieve a specific face recognition log by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the face recognition log

**Response**
```json
{
  "log": {
    "id": "string",
    "camera_id": "string",
    "edge_device_id": "string",
    "device_id": "string",
    "user_id": "string",
    "status": "string",
    "similarity_percent": 95.5,
    "timestamp": "2023-01-01T00:00:00.000Z",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z",
    "created_by": "string"
  }
}
```

**Error Responses**
- `400 Bad Request`: When log ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the face recognition log with the provided ID doesn't exist
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `FaceRecognitionLogsController.getFaceRecognitionLogById`

---

### Log Recognition Event

**POST** `/api/face-recognition-logs/`

Create a new face recognition log entry.

**Request Body**
```json
{
  "camera_id": "string",      // required
  "edge_device_id": "string", // required
  "device_id": "string",     // required
  "user_id": "string",       // optional
  "status": "string",        // required (e.g., "success", "failed")
  "similarity_percent": 95.5, // optional
  "timestamp": "2023-01-01T00:00:00.000Z" // optional
}
```

**Response**
```json
{
  "log": {
    "id": "string",
    "camera_id": "string",
    "edge_device_id": "string",
    "device_id": "string",
    "user_id": "string",
    "status": "string",
    "similarity_percent": 95.5,
    "timestamp": "2023-01-01T00:00:00.000Z",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z",
    "created_by": "string"
  }
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `FaceRecognitionLogsController.logRecognitionEvent`

---

### Delete Face Recognition Log

**DELETE** `/api/face-recognition-logs/:id`

Delete a face recognition log by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the face recognition log to delete

**Error Responses**
- `400 Bad Request`: When log ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the face recognition log with the provided ID doesn't exist
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `FaceRecognitionLogsController.deleteFaceRecognitionLog`

---

### Get Logs By User ID

**GET** `/api/face-recognition-logs/user/:userId`

Retrieve all face recognition logs for a specific user.

**Path Parameters**
- `userId` (required): The ID of the user

**Response**
```json
{
  "logs": [
    {
      "id": "string",
      "camera_id": "string",
      "edge_device_id": "string",
      "device_id": "string",
      "user_id": "string",
      "status": "string",
      "similarity_percent": 95.5,
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When user ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Get Logs By Camera ID

**GET** `/api/face-recognition-logs/camera/:cameraId`

Retrieve all face recognition logs for a specific camera.

**Path Parameters**
- `cameraId` (required): The ID of the camera

**Response**
```json
{
  "logs": [
    {
      "id": "string",
      "camera_id": "string",
      "edge_device_id": "string",
      "device_id": "string",
      "user_id": "string",
      "status": "string",
      "similarity_percent": 95.5,
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When camera ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Get Logs By Edge Device ID

**GET** `/api/face-recognition-logs/edge-device/:edgeDeviceId`

Retrieve all face recognition logs for a specific edge device.

**Path Parameters**
- `edgeDeviceId` (required): The ID of the edge device

**Response**
```json
{
  "logs": [
    {
      "id": "string",
      "camera_id": "string",
      "edge_device_id": "string",
      "device_id": "string",
      "user_id": "string",
      "status": "string",
      "similarity_percent": 95.5,
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When edge device ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Get Logs By Status

**GET** `/api/face-recognition-logs/status/:status`

Retrieve all face recognition logs with a specific status.

**Path Parameters**
- `status` (required): The status to filter by (e.g., "success", "failed")

**Response**
```json
{
  "logs": [
    {
      "id": "string",
      "camera_id": "string",
      "edge_device_id": "string",
      "device_id": "string",
      "user_id": "string",
      "status": "string",
      "similarity_percent": 95.5,
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When status is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Get Logs By Similarity Range

**GET** `/api/face-recognition-logs/similarity/:minSimilarity/:maxSimilarity`

Retrieve all face recognition logs with similarity percentage within a specific range.

**Path Parameters**
- `minSimilarity` (required): The minimum similarity percentage
- `maxSimilarity` (required): The maximum similarity percentage

**Response**
```json
{
  "logs": [
    {
      "id": "string",
      "camera_id": "string",
      "edge_device_id": "string",
      "device_id": "string",
      "user_id": "string",
      "status": "string",
      "similarity_percent": 95.5,
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When min or max similarity values are not provided or are invalid
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Get Logs By Date Range

**GET** `/api/face-recognition-logs/date-range/:startDate/:endDate`

Retrieve all face recognition logs within a specific date range.

**Path Parameters**
- `startDate` (required): The start date in ISO format (YYYY-MM-DD)
- `endDate` (required): The end date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "logs": [
    {
      "id": "string",
      "camera_id": "string",
      "edge_device_id": "string",
      "device_id": "string",
      "user_id": "string",
      "status": "string",
      "similarity_percent": 95.5,
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When start date or end date are not provided or have an invalid format
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Get Logs By User ID and Date Range

**GET** `/api/face-recognition-logs/user/:userId/date-range/:startDate/:endDate`

Retrieve all face recognition logs for a specific user within a date range.

**Path Parameters**
- `userId` (required): The ID of the user
- `startDate` (required): The start date in ISO format (YYYY-MM-DD)
- `endDate` (required): The end date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "logs": [
    {
      "id": "string",
      "camera_id": "string",
      "edge_device_id": "string",
      "device_id": "string",
      "user_id": "string",
      "status": "string",
      "similarity_percent": 95.5,
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When user ID, start date, or end date are not provided or dates have an invalid format
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Generate User Recognition Summary

**GET** `/api/face-recognition-logs/user/:userId/summary/:startDate/:endDate`

Generate a summary of face recognition events for a specific user within a date range.

**Path Parameters**
- `userId` (required): The ID of the user
- `startDate` (required): The start date in ISO format (YYYY-MM-DD)
- `endDate` (required): The end date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "summary": {
    "userId": "string",
    "totalRecognitions": 42,
    "successfulRecognitions": 40,
    "failedRecognitions": 2,
    "averageSimilarityPercent": 96.7,
    "timeRange": {
      "start": "2023-01-01T00:00:00.000Z",
      "end": "2023-01-31T23:59:59.000Z"
    },
    "recognitionsByDay": [
      {
        "date": "2023-01-01",
        "count": 5,
        "successCount": 5,
        "failCount": 0
      },
      {
        "date": "2023-01-02",
        "count": 3,
        "successCount": 2,
        "failCount": 1
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When user ID, start date, or end date are not provided or dates have an invalid format
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user