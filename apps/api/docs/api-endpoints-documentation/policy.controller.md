# Policy API Documentation

Base API path: `/api/policies`

This API provides endpoints for managing policies in the system. Policies define authorization rules for various resources and actions. All endpoints require authorization.

## Endpoints

### Get All Policies

**GET** `/api/policies/`

Retrieve a list of all policies based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of policies to return
- `skip` (optional): Number of policies to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "success": true,
  "data": {
    "policies": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "type": "string",
        "resource": "string",
        "action": "string",
        "effect": "string",
        "conditions": {},
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.getPolicies`

---

### Get Policy By ID

**GET** `/api/policies/:id`

Retrieve a specific policy by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the policy

**Response**
```json
{
  "success": true,
  "data": {
    "policy": {
      "id": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "resource": "string",
      "action": "string",
      "effect": "string",
      "conditions": {},
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When policy ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the policy with the provided ID doesn't exist
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.getPolicyById`

---

### Create Policy

**POST** `/api/policies/`

Create a new policy.

**Request Body**
```json
{
  "name": "string",         // required - Unique name for the policy
  "description": "string", // optional - Description of the policy
  "type": "string",        // required - Type of policy (e.g., "role", "user")
  "resource": "string",    // required - Resource the policy applies to
  "action": "string",      // required - Action being controlled (e.g., "read", "write")
  "effect": "string",      // required - Effect of the policy (e.g., "allow", "deny")
  "conditions": {}         // optional - JSON object with additional conditions
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "policy": {
      "id": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "resource": "string",
      "action": "string",
      "effect": "string",
      "conditions": {},
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  },
  "message": "Policy created successfully"
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated
- `409 Conflict`: When a policy with the same name already exists
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.createPolicy`

---

### Update Policy

**PUT** `/api/policies/:id`

Update an existing policy.

**Path Parameters**
- `id` (required): The unique identifier of the policy to update

**Request Body**
```json
{
  "name": "string",         // optional - New name for the policy
  "description": "string", // optional - New description of the policy
  "type": "string",        // optional - New type of policy
  "resource": "string",    // optional - New resource the policy applies to
  "action": "string",      // optional - New action being controlled
  "effect": "string",      // optional - New effect of the policy
  "conditions": {}         // optional - New JSON object with additional conditions
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "policy": {
      "id": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "resource": "string",
      "action": "string",
      "effect": "string",
      "conditions": {},
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "updated_by": "string",
      "created_by": "string"
    }
  },
  "message": "Policy updated successfully"
}
```

**Error Responses**
- `400 Bad Request`: When policy ID is not provided or update data is invalid
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the policy with the provided ID doesn't exist
- `409 Conflict`: When the new policy name conflicts with an existing policy
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.updatePolicy`

---

### Delete Policy

**DELETE** `/api/policies/:id`

Delete a policy by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the policy to delete

**Response**
```json
{
  "success": true,
  "data": null,
  "message": "Policy deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When policy ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the policy with the provided ID doesn't exist
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.deletePolicy`

---

### Get Policies By Type

**GET** `/api/policies/type/:type`

Retrieve all policies of a specific type.

**Path Parameters**
- `type` (required): The type of policies to retrieve (e.g., "role", "user")

**Response**
```json
{
  "success": true,
  "data": {
    "policies": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "type": "string",
        "resource": "string",
        "action": "string",
        "effect": "string",
        "conditions": {},
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When type is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.getPoliciesByType`

---

### Get Policies By Resource

**GET** `/api/policies/resource/:resource`

Retrieve all policies for a specific resource.

**Path Parameters**
- `resource` (required): The resource to filter policies by

**Response**
```json
{
  "success": true,
  "data": {
    "policies": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "type": "string",
        "resource": "string",
        "action": "string",
        "effect": "string",
        "conditions": {},
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When resource is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.getPoliciesByResource`

---

### Get Policies For User

**GET** `/api/policies/user/:userId`

Retrieve all policies applicable to a specific user.

**Path Parameters**
- `userId` (required): The ID of the user

**Response**
```json
{
  "success": true,
  "data": {
    "policies": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "type": "string",
        "resource": "string",
        "action": "string",
        "effect": "string",
        "conditions": {},
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When user ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.getPoliciesForUser`

---

### Assign Policy To Role

**POST** `/api/policies/assign/role`

Assign a policy to a role.

**Request Body**
```json
{
  "policyId": "string", // required - ID of the policy to assign
  "roleId": "string"    // required - ID of the role to assign the policy to
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "rolePolicyAssignment": {
      "id": "string",
      "role_id": "string",
      "policy_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  },
  "message": "Policy assigned to role successfully"
}
```

**Error Responses**
- `400 Bad Request`: When policy ID or role ID are not provided
- `401 Unauthorized`: When the user is not authenticated
- `409 Conflict`: When the policy is already assigned to the role
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.assignPolicyToRole`

---

### Remove Policy From Role

**DELETE** `/api/policies/:policyId/role/:roleId`

Remove a policy from a role.

**Path Parameters**
- `policyId` (required): The ID of the policy
- `roleId` (required): The ID of the role

**Response**
```json
{
  "success": true,
  "data": null,
  "message": "Policy removed from role successfully"
}
```

**Error Responses**
- `400 Bad Request`: When policy ID or role ID are not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the policy-role assignment doesn't exist
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.removePolicyFromRole`

---

### Get Policy Statements For Token

**GET** `/api/policies/token-statements/:userId`

Generate policy statements for a user's authentication token.

**Path Parameters**
- `userId` (required): The ID of the user

**Response**
```json
{
  "success": true,
  "data": {
    "statements": [
      {
        "resource": "string",
        "action": "string",
        "effect": "string",
        "conditions": {}
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When user ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `PolicyController.getPolicyStatementsForToken`