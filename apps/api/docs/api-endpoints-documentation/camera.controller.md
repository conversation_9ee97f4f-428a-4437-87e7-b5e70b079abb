# Camera API Documentation

Base API path: `/api/cameras`

This API provides endpoints for managing cameras in the system. All endpoints require authorization.

## Endpoints

### Get All Cameras

**GET** `/api/cameras/`

Retrieve a list of all cameras based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of cameras to return
- `skip` (optional): Number of cameras to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "success": true,
  "data": {
    "cameras": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "status": "string",
        "location": "string",
        "ip_address": "string",
        "model": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Authorization**
Needs policy for `CameraController.getCameras`

---

### Get Cameras by Type

**GET** `/api/cameras/type/:type`

Retrieve cameras of a specific type.

**Path Parameters**
- `type` (required): The type of camera

**Response**
```json
{
  "success": true,
  "data": {
    "cameras": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "status": "string",
        "location": "string",
        "ip_address": "string",
        "model": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When camera type is not provided

**Authorization**
Needs policy for `CameraController.getCamerasByType`

---

### Get Cameras by Status

**GET** `/api/cameras/status/:status`

Retrieve cameras with a specific status.

**Path Parameters**
- `status` (required): The status of camera (e.g., "active", "inactive", "maintenance")

**Response**
```json
{
  "success": true,
  "data": {
    "cameras": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "status": "string",
        "location": "string",
        "ip_address": "string",
        "model": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When camera status is not provided

**Authorization**
Needs policy for `CameraController.getCamerasByStatus`

---

### Get Cameras by Location

**GET** `/api/cameras/location/:location`

Retrieve cameras at a specific location.

**Path Parameters**
- `location` (required): The location of the cameras

**Response**
```json
{
  "success": true,
  "data": {
    "cameras": [
      {
        "id": "string",
        "name": "string",
        "type": "string",
        "status": "string",
        "location": "string",
        "ip_address": "string",
        "model": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When location is not provided

**Authorization**
Needs policy for `CameraController.getCamerasByLocation`

---

### Get Camera by IP Address

**GET** `/api/cameras/ip/:ipAddress`

Retrieve a camera by its IP address.

**Path Parameters**
- `ipAddress` (required): The IP address of the camera

**Response**
```json
{
  "success": true,
  "data": {
    "camera": {
      "id": "string",
      "name": "string",
      "type": "string",
      "status": "string",
      "location": "string",
      "ip_address": "string",
      "model": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When IP address is not provided
- `404 Not Found`: When no camera is found with the provided IP address

**Authorization**
Needs policy for `CameraController.getCameraByIpAddress`

---

### Get Camera by ID

**GET** `/api/cameras/:id`

Retrieve a specific camera by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the camera

**Response**
```json
{
  "success": true,
  "data": {
    "camera": {
      "id": "string",
      "name": "string",
      "type": "string",
      "status": "string",
      "location": "string",
      "ip_address": "string",
      "model": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When camera ID is not provided
- `404 Not Found`: When the camera with the provided ID doesn't exist

**Authorization**
Needs policy for `CameraController.getCameraById`

---

### Create Camera

**POST** `/api/cameras/`

Create a new camera.

**Request Body**
```json
{
  "name": "string",         // required
  "type": "string",         // optional
  "status": "string",       // optional
  "location": "string",     // required
  "ip_address": "string",   // required
  "model": "string"         // optional
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "camera": {
      "id": "string",
      "name": "string",
      "type": "string",
      "status": "string",
      "location": "string",
      "ip_address": "string",
      "model": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  },
  "message": "Camera created successfully"
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `CameraController.createCamera`

---

### Update Camera

**PUT** `/api/cameras/:id`

Update an existing camera.

**Path Parameters**
- `id` (required): The unique identifier of the camera to update

**Request Body**
```json
{
  "name": "string",         // optional
  "type": "string",         // optional
  "status": "string",       // optional
  "location": "string",     // optional
  "ip_address": "string",   // optional
  "model": "string"         // optional
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Camera updated successfully"
}
```

**Error Responses**
- `400 Bad Request`: When camera ID is not provided or update fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `CameraController.updateCamera`

---

### Delete Camera

**DELETE** `/api/cameras/:id`

Delete a camera by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the camera to delete

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Camera deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When camera ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `CameraController.deleteCamera`