# Member Role API Documentation

Base API path: `/api/member-roles`

This API provides endpoints for managing user role assignments in the system. All endpoints require authorization.

## Endpoints

### Get All Member Roles

**GET** `/api/member-roles/`

Retrieve a list of all member roles based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of member roles to return
- `skip` (optional): Number of member roles to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "success": true,
  "data": {
    "memberRoles": [
      {
        "id": "string",
        "user_id": "string",
        "role_id": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `MemberRoleController.getMemberRoles`

---

### Get Member Role By ID

**GET** `/api/member-roles/:id`

Retrieve a specific member role by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the member role

**Response**
```json
{
  "success": true,
  "data": {
    "memberRole": {
      "id": "string",
      "user_id": "string",
      "role_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When member role ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the member role with the provided ID doesn't exist

**Authorization**
Needs policy for `MemberRoleController.getMemberRoleById`

---

### Assign Role To User

**POST** `/api/member-roles/assign`

Assign a role to a user.

**Request Body**
```json
{
  "userIdToAssign": "string", // required - User ID to assign the role to
  "roleId": "string"         // required - Role ID to assign
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "memberRole": {
      "id": "string",
      "user_id": "string",
      "role_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  },
  "message": "Role assigned to user successfully"
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `MemberRoleController.assignRoleToUser`

---

### Remove Role From User

**DELETE** `/api/member-roles/user/:userId/role/:roleId`

Remove a role from a user.

**Path Parameters**
- `userId` (required): The ID of the user
- `roleId` (required): The ID of the role to remove

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Role removed from user successfully"
}
```

**Error Responses**
- `400 Bad Request`: When user ID or role ID are not provided or removal fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `MemberRoleController.removeRoleFromUser`

---

### Get Roles For User

**GET** `/api/member-roles/user/:userId`

Get all roles assigned to a specific user.

**Path Parameters**
- `userId` (required): The ID of the user

**Response**
```json
{
  "success": true,
  "data": {
    "memberRoles": [
      {
        "id": "string",
        "user_id": "string",
        "role_id": "string",
        "role": {
          "id": "string",
          "name": "string",
          "description": "string"
        },
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When user ID is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `MemberRoleController.getRolesForUser`

---

### Get Users With Role

**GET** `/api/member-roles/role/:roleId`

Get all users assigned to a specific role.

**Path Parameters**
- `roleId` (required): The ID of the role

**Response**
```json
{
  "success": true,
  "data": {
    "memberRoles": [
      {
        "id": "string",
        "user_id": "string",
        "role_id": "string",
        "user": {
          "id": "string",
          "username": "string",
          "email": "string",
          "first_name": "string",
          "last_name": "string"
        },
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When role ID is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `MemberRoleController.getUsersWithRole`

---

### Remove All Roles From User

**DELETE** `/api/member-roles/user/:userId/roles`

Remove all roles assigned to a specific user.

**Path Parameters**
- `userId` (required): The ID of the user

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "All roles removed from user successfully"
}
```

**Error Responses**
- `400 Bad Request`: When user ID is not provided or removal fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Requires a valid authenticated user

---

### Remove Role From All Users

**DELETE** `/api/member-roles/role/:roleId/users`

Remove a specific role from all users who have it assigned.

**Path Parameters**
- `roleId` (required): The ID of the role

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Role removed from all users successfully"
}
```

**Error Responses**
- `400 Bad Request`: When role ID is not provided or removal fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Requires a valid authenticated user