# Shift Detail API Documentation

Base API path: `/api/shift-details`

This API provides endpoints for managing shift details in the system. All endpoints require authorization.

## Endpoints

### Get All Shift Details

**GET** `/api/shift-details/`

Retrieve a list of all shift details based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of shift details to return
- `skip` (optional): Number of shift details to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "shiftDetails": [
    {
      "id": "string",
      "shift_id": "string",
      "check_in_start_time": "08:00:00",
      "check_in_end_time": "08:30:00",
      "check_out_start_time": "17:00:00",
      "check_out_end_time": "17:30:00",
      "total_working_hours": 8.5,
      "is_overnight": false,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `ShiftDetailController.getShiftDetails`

---

### Get Shift Detail By ID

**GET** `/api/shift-details/:id`

Retrieve a specific shift detail by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the shift detail

**Response**
```json
{
  "shiftDetail": {
    "id": "string",
    "shift_id": "string",
    "check_in_start_time": "08:00:00",
    "check_in_end_time": "08:30:00",
    "check_out_start_time": "17:00:00",
    "check_out_end_time": "17:30:00",
    "total_working_hours": 8.5,
    "is_overnight": false,
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
}
```

**Error Responses**
- `400 Bad Request`: When shift detail ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the shift detail with the provided ID doesn't exist
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `ShiftDetailController.getShiftDetailById`

---

### Create Shift Detail

**POST** `/api/shift-details/`

Create a new shift detail.

**Request Body**
```json
{
  "shift_id": "string",                // required - The ID of the shift this detail belongs to
  "check_in_start_time": "08:00:00",  // required - Start time for check-in window
  "check_in_end_time": "08:30:00",    // optional - End time for check-in window
  "check_out_start_time": "17:00:00", // required - Start time for check-out window
  "check_out_end_time": "17:30:00",   // optional - End time for check-out window
  "total_working_hours": 8.5,         // required - Total working hours for this shift detail
  "is_overnight": false               // optional - Whether this shift spans overnight
}
```

**Response**
```json
{
  "shiftDetail": {
    "id": "string",
    "shift_id": "string",
    "check_in_start_time": "08:00:00",
    "check_in_end_time": "08:30:00",
    "check_out_start_time": "17:00:00",
    "check_out_end_time": "17:30:00",
    "total_working_hours": 8.5,
    "is_overnight": false,
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `ShiftDetailController.createShiftDetail`

---

### Update Shift Detail

**PUT** `/api/shift-details/:id`

Update an existing shift detail.

**Path Parameters**
- `id` (required): The unique identifier of the shift detail to update

**Request Body**
```json
{
  "shift_id": "string",                // optional - The ID of the shift this detail belongs to
  "check_in_start_time": "08:00:00",  // optional - Start time for check-in window
  "check_in_end_time": "08:30:00",    // optional - End time for check-in window
  "check_out_start_time": "17:00:00", // optional - Start time for check-out window
  "check_out_end_time": "17:30:00",   // optional - End time for check-out window
  "total_working_hours": 8.5,         // optional - Total working hours for this shift detail
  "is_overnight": false               // optional - Whether this shift spans overnight
}
```

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When shift detail ID is not provided or update fails
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `ShiftDetailController.updateShiftDetail`

---

### Delete Shift Detail

**DELETE** `/api/shift-details/:id`

Delete a shift detail by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the shift detail to delete

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When shift detail ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `ShiftDetailController.deleteShiftDetail`

---

### Get Shift Details By Shift ID

**GET** `/api/shift-details/shift/:shiftId`

Retrieve all shift details associated with a specific shift.

**Path Parameters**
- `shiftId` (required): The ID of the shift

**Response**
```json
{
  "shiftDetails": [
    {
      "id": "string",
      "shift_id": "string",
      "check_in_start_time": "08:00:00",
      "check_in_end_time": "08:30:00",
      "check_out_start_time": "17:00:00",
      "check_out_end_time": "17:30:00",
      "total_working_hours": 8.5,
      "is_overnight": false,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When shift ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Get Overnight Shifts

**GET** `/api/shift-details/overnight`

Retrieve all shift details that are configured for overnight shifts.

**Response**
```json
{
  "shiftDetails": [
    {
      "id": "string",
      "shift_id": "string",
      "check_in_start_time": "22:00:00",
      "check_in_end_time": "22:30:00",
      "check_out_start_time": "06:00:00",
      "check_out_end_time": "06:30:00",
      "total_working_hours": 8.0,
      "is_overnight": true,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Get Shift Details By Working Hours

**GET** `/api/shift-details/working-hours/:hours`

Retrieve all shift details with a specific number of total working hours.

**Path Parameters**
- `hours` (required): The total working hours to filter by

**Response**
```json
{
  "shiftDetails": [
    {
      "id": "string",
      "shift_id": "string",
      "check_in_start_time": "08:00:00",
      "check_in_end_time": "08:30:00",
      "check_out_start_time": "17:00:00",
      "check_out_end_time": "17:30:00",
      "total_working_hours": 8.5,
      "is_overnight": false,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When hours parameter is not provided or invalid
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Delete Shift Details By Shift ID

**DELETE** `/api/shift-details/shift/:shiftId`

Delete all shift details associated with a specific shift.

**Path Parameters**
- `shiftId` (required): The ID of the shift

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When shift ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user