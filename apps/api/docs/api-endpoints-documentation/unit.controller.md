# Unit Controller API Documentation

Base path: `/api/units`

## Endpoints

### 1. Get All Units

**Endpoint:** `GET /api/units/`

**Description:** Retrieves a list of all organizational units with optional pagination and sorting.

**Parameters:**
- Query Parameters:
  - `limit` (optional): Number of units to retrieve
  - `skip` (optional): Number of units to skip
  - `sortBy` (optional): Field to sort by
  - `sortDirection` (optional): Sort direction (asc/desc)

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "units": [
      {
        "id": "string",
        "name": "string",
        "organization_id": "string",
        "parent_unit_id": "string", // optional
        "created_at": "timestamp",
        "created_by": "string",
        "updated_at": "timestamp"
      }
    ]
  }
}
```

### 2. Get Unit By ID

**Endpoint:** `GET /api/units/:id`

**Description:** Retrieves a specific organizational unit by its ID.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the unit

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "unit": {
      "id": "string",
      "name": "string",
      "organization_id": "string",
      "parent_unit_id": "string", // optional
      "created_at": "timestamp",
      "created_by": "string",
      "updated_at": "timestamp"
    }
  }
}
```

**Error Responses:**
- `400 Bad Request`: If unit ID is missing
- `404 Not Found`: If the unit is not found

### 3. Create Unit

**Endpoint:** `POST /api/units/`

**Description:** Creates a new organizational unit.

**Parameters:**
- Body:
  ```json
  {
    "name": "string",
    "organization_id": "string",
    "parent_unit_id": "string" // optional
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "unit": {
      "id": "string",
      "name": "string",
      "organization_id": "string",
      "parent_unit_id": "string", // if provided
      "created_at": "timestamp",
      "created_by": "string"
    }
  },
  "message": "Unit created successfully"
}
```

**Error Responses:**
- `400 Bad Request`: If required fields are missing

### 4. Update Unit

**Endpoint:** `PUT /api/units/:id`

**Description:** Updates a specific organizational unit's information.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the unit
- Body: Unit data to update (fields that can be updated)
  ```json
  {
    "name": "string",
    "parent_unit_id": "string" // optional
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Unit updated successfully"
}
```

**Error Responses:**
- `400 Bad Request`: If unit ID is missing or if the update fails

### 5. Delete Unit

**Endpoint:** `DELETE /api/units/:id`

**Description:** Deletes a specific organizational unit.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the unit

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Unit deleted successfully"
}
```

**Error Responses:**
- `400 Bad Request`: If unit ID is missing

### 6. Get Organizational Hierarchy

**Endpoint:** `GET /api/units/organization/:organizationId/hierarchy`

**Description:** Retrieves the complete organizational hierarchy for a specific organization.

**Parameters:**
- Path Parameters:
  - `organizationId` (required): ID of the organization

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "hierarchy": [
      {
        "id": "string",
        "name": "string",
        "organization_id": "string",
        "children": [ // Array of child units with same structure (recursive)
          {
            "id": "string",
            "name": "string",
            "children": []
          }
        ]
      }
    ]
  }
}
```

**Error Responses:**
- `400 Bad Request`: If organization ID is missing

### 7. Get Units By Organization ID

**Endpoint:** `GET /api/units/organization/:organizationId`

**Description:** Retrieves units belonging to a specific organization.

**Parameters:**
- Path Parameters:
  - `organizationId` (required): ID of the organization

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "units": [
      {
        "id": "string",
        "name": "string",
        "organization_id": "string",
        "parent_unit_id": "string", // optional
        "created_at": "timestamp",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses:**
- `400 Bad Request`: If organization ID is missing

### 8. Get Units By User ID

**Endpoint:** `GET /api/units/user/:userId`

**Description:** Retrieves units associated with a specific user.

**Parameters:**
- Path Parameters:
  - `userId` (required): ID of the user

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "units": [
      {
        "id": "string",
        "name": "string",
        "organization_id": "string",
        "parent_unit_id": "string", // optional
        "created_at": "timestamp",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses:**
- `400 Bad Request`: If user ID is missing

### 9. Get Units By Parent Unit ID

**Endpoint:** `GET /api/units/parent/:parentUnitId`

**Description:** Retrieves units that are children of a specific parent unit.

**Parameters:**
- Path Parameters:
  - `parentUnitId` (required): ID of the parent unit

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "units": [
      {
        "id": "string",
        "name": "string",
        "organization_id": "string",
        "parent_unit_id": "string",
        "created_at": "timestamp",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses:**
- `400 Bad Request`: If parent unit ID is missing

### 10. Get Root Units

**Endpoint:** `GET /api/units/roots`

**Description:** Retrieves all root units (units without a parent).

**Parameters:** None

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "units": [
      {
        "id": "string",
        "name": "string",
        "organization_id": "string",
        "created_at": "timestamp",
        "created_by": "string"
      }
    ]
  }
}
```

### 11. Get Units By Tenant ID

**Endpoint:** `GET /api/units/tenant/:tenantId`

**Description:** Retrieves units associated with a specific tenant.

**Parameters:**
- Path Parameters:
  - `tenantId` (required): ID of the tenant

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "units": [
      {
        "id": "string",
        "name": "string",
        "organization_id": "string",
        "parent_unit_id": "string", // optional
        "created_at": "timestamp",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses:**
- `400 Bad Request`: If tenant ID is missing

### 12. Update Users' Unit

**Endpoint:** `PUT /api/units/users/bulk-update`

**Description:** Updates the unit for multiple users at once.

**Parameters:**
- Body:
  ```json
  {
    "userIds": ["string", "string"],
    "unitId": "string"
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "updatedCount": "number",
    "updatedUsers": ["string"]
  },
  "message": "Successfully updated unit for X user(s)"
}
```

**Error Responses:**
- `400 Bad Request`: If required fields are missing, if userIds is not an array, or if unitId is not a valid string