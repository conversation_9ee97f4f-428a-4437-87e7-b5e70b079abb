# Edge Device Info API Documentation

Base API path: `/api/edge-device-info`

This API provides endpoints for managing edge device information in the system. All endpoints require authorization.

## Endpoints

### Get All Edge Device Info

**GET** `/api/edge-device-info/`

Retrieve a list of all edge device information entries based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of entries to return
- `skip` (optional): Number of entries to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "success": true,
  "data": {
    "deviceInfoList": [
      {
        "id": "string",
        "edge_device_id": "string",
        "cpu_model": "string",
        "cpu_cores": 4,
        "cpu_usage": 25.5,
        "memory_total": 8192,
        "memory_used": 4096,
        "disk_total": 500000,
        "disk_used": 250000,
        "os_name": "string",
        "os_version": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceInfoController.getEdgeDeviceInfoList`

---

### Get Edge Device Info By ID

**GET** `/api/edge-device-info/:id`

Retrieve a specific edge device information entry by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the edge device info entry

**Response**
```json
{
  "success": true,
  "data": {
    "deviceInfo": {
      "id": "string",
      "edge_device_id": "string",
      "cpu_model": "string",
      "cpu_cores": 4,
      "cpu_usage": 25.5,
      "memory_total": 8192,
      "memory_used": 4096,
      "disk_total": 500000,
      "disk_used": 250000,
      "os_name": "string",
      "os_version": "string",
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When edge device info ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the edge device info with the provided ID doesn't exist

**Authorization**
Needs policy for `EdgeDeviceInfoController.getEdgeDeviceInfoById`

---

### Get Info By Edge Device ID

**GET** `/api/edge-device-info/device/:edgeDeviceId`

Retrieve all information entries for a specific edge device.

**Path Parameters**
- `edgeDeviceId` (required): The ID of the edge device

**Response**
```json
{
  "success": true,
  "data": {
    "deviceInfoList": [
      {
        "id": "string",
        "edge_device_id": "string",
        "cpu_model": "string",
        "cpu_cores": 4,
        "cpu_usage": 25.5,
        "memory_total": 8192,
        "memory_used": 4096,
        "disk_total": 500000,
        "disk_used": 250000,
        "os_name": "string",
        "os_version": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When edge device ID is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceInfoController.getInfoByEdgeDeviceId`

---

### Get Latest Info By Edge Device ID

**GET** `/api/edge-device-info/device/:edgeDeviceId/latest`

Retrieve the most recent information entry for a specific edge device.

**Path Parameters**
- `edgeDeviceId` (required): The ID of the edge device

**Response**
```json
{
  "success": true,
  "data": {
    "deviceInfo": {
      "id": "string",
      "edge_device_id": "string",
      "cpu_model": "string",
      "cpu_cores": 4,
      "cpu_usage": 25.5,
      "memory_total": 8192,
      "memory_used": 4096,
      "disk_total": 500000,
      "disk_used": 250000,
      "os_name": "string",
      "os_version": "string",
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When edge device ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When no information exists for the provided edge device ID

**Authorization**
Needs policy for `EdgeDeviceInfoController.getLatestInfoByEdgeDeviceId`

---

### Get Info By CPU Model

**GET** `/api/edge-device-info/cpu-model/:cpuModel`

Retrieve all information entries for edge devices with a specific CPU model.

**Path Parameters**
- `cpuModel` (required): The CPU model to filter by

**Response**
```json
{
  "success": true,
  "data": {
    "deviceInfoList": [
      {
        "id": "string",
        "edge_device_id": "string",
        "cpu_model": "string",
        "cpu_cores": 4,
        "cpu_usage": 25.5,
        "memory_total": 8192,
        "memory_used": 4096,
        "disk_total": 500000,
        "disk_used": 250000,
        "os_name": "string",
        "os_version": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When CPU model is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceInfoController.getInfoByCpuModel`

---

### Get Info By OS

**GET** `/api/edge-device-info/os/:os`

Retrieve all information entries for edge devices with a specific operating system.

**Path Parameters**
- `os` (required): The operating system to filter by

**Response**
```json
{
  "success": true,
  "data": {
    "deviceInfoList": [
      {
        "id": "string",
        "edge_device_id": "string",
        "cpu_model": "string",
        "cpu_cores": 4,
        "cpu_usage": 25.5,
        "memory_total": 8192,
        "memory_used": 4096,
        "disk_total": 500000,
        "disk_used": 250000,
        "os_name": "string",
        "os_version": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When OS is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceInfoController.getInfoByOs`

---

### Get Info By Date Range

**GET** `/api/edge-device-info/date-range/:startDate/:endDate`

Retrieve all information entries within a specific date range.

**Path Parameters**
- `startDate` (required): The start date in ISO format (YYYY-MM-DD)
- `endDate` (required): The end date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "success": true,
  "data": {
    "deviceInfoList": [
      {
        "id": "string",
        "edge_device_id": "string",
        "cpu_model": "string",
        "cpu_cores": 4,
        "cpu_usage": 25.5,
        "memory_total": 8192,
        "memory_used": 4096,
        "disk_total": 500000,
        "disk_used": 250000,
        "os_name": "string",
        "os_version": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When start date or end date are not provided or have an invalid format
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceInfoController.getInfoByDateRange`

---

### Get Info By Edge Device ID and Date Range

**GET** `/api/edge-device-info/device/:edgeDeviceId/date-range/:startDate/:endDate`

Retrieve all information entries for a specific edge device within a date range.

**Path Parameters**
- `edgeDeviceId` (required): The ID of the edge device
- `startDate` (required): The start date in ISO format (YYYY-MM-DD)
- `endDate` (required): The end date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "success": true,
  "data": {
    "deviceInfoList": [
      {
        "id": "string",
        "edge_device_id": "string",
        "cpu_model": "string",
        "cpu_cores": 4,
        "cpu_usage": 25.5,
        "memory_total": 8192,
        "memory_used": 4096,
        "disk_total": 500000,
        "disk_used": 250000,
        "os_name": "string",
        "os_version": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When edge device ID, start date, or end date are not provided or dates have an invalid format
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceInfoController.getInfoByEdgeDeviceIdAndDateRange`

---

### Create Edge Device Info

**POST** `/api/edge-device-info/`

Create a new edge device information entry.

**Request Body**
```json
{
  "edge_device_id": "string",    // required
  "cpu_model": "string",        // required
  "cpu_cores": 4,               // required
  "cpu_usage": 25.5,            // required
  "memory_total": 8192,         // required
  "memory_used": 4096,          // required
  "disk_total": 500000,         // required
  "disk_used": 250000,          // required
  "os_name": "string",          // required
  "os_version": "string",       // required
  "timestamp": "2023-01-01T00:00:00.000Z" // optional
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "deviceInfo": {
      "id": "string",
      "edge_device_id": "string",
      "cpu_model": "string",
      "cpu_cores": 4,
      "cpu_usage": 25.5,
      "memory_total": 8192,
      "memory_used": 4096,
      "disk_total": 500000,
      "disk_used": 250000,
      "os_name": "string",
      "os_version": "string",
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  },
  "message": "Edge device information created successfully"
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceInfoController.createEdgeDeviceInfo`

---

### Update Edge Device Info

**PUT** `/api/edge-device-info/:id`

Update an existing edge device information entry.

**Path Parameters**
- `id` (required): The unique identifier of the edge device info to update

**Request Body**
```json
{
  "edge_device_id": "string",    // optional
  "cpu_model": "string",        // optional
  "cpu_cores": 4,               // optional
  "cpu_usage": 25.5,            // optional
  "memory_total": 8192,         // optional
  "memory_used": 4096,          // optional
  "disk_total": 500000,         // optional
  "disk_used": 250000,          // optional
  "os_name": "string",          // optional
  "os_version": "string",       // optional
  "timestamp": "2023-01-01T00:00:00.000Z" // optional
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Edge device information updated successfully"
}
```

**Error Responses**
- `400 Bad Request`: When edge device info ID is not provided or update fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceInfoController.updateEdgeDeviceInfo`

---

### Delete Edge Device Info

**DELETE** `/api/edge-device-info/:id`

Delete an edge device information entry by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the edge device info to delete

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Edge device information deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When edge device info ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceInfoController.deleteEdgeDeviceInfo`

---

### Delete Info By Edge Device ID

**DELETE** `/api/edge-device-info/device/:edgeDeviceId`

Delete all information entries for a specific edge device.

**Path Parameters**
- `edgeDeviceId` (required): The ID of the edge device

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Edge device information deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When edge device ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceInfoController.deleteInfoByEdgeDeviceId`