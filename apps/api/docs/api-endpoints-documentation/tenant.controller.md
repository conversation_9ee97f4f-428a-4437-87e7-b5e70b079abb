# Tenant Controller API Documentation

Base path: `/api/tenants`

## Endpoints

### 1. Get All Organizations

**Endpoint:** `GET /api/tenants/`

**Description:** Retrieves a list of all organizations with optional pagination and sorting.

**Parameters:**
- Query Parameters:
  - `limit` (optional): Number of organizations to retrieve
  - `skip` (optional): Number of organizations to skip
  - `sortBy` (optional): Field to sort by
  - `sortDirection` (optional): Sort direction (asc/desc)

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "organizations": [
      {
        "id": "string",
        "name": "string",
        "created_at": "timestamp",
        "created_by": "string",
        "updated_at": "timestamp"
      }
    ]
  }
}
```

**Error Responses:**
- `401 Unauthorized`: If the user is not authenticated
- `500 Internal Server Error`: If there is a server error

### 2. Get Organization By ID

**Endpoint:** `GET /api/tenants/:id`

**Description:** Retrieves a specific organization by its ID.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the organization

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "organization": {
      "id": "string",
      "name": "string",
      "created_at": "timestamp",
      "created_by": "string",
      "updated_at": "timestamp"
    }
  }
}
```

**Error Responses:**
- `400 Bad Request`: If organization ID is missing
- `401 Unauthorized`: If the user is not authenticated
- `404 Not Found`: If the organization is not found
- `500 Internal Server Error`: If there is a server error

### 3. Create Organization

**Endpoint:** `POST /api/tenants/`

**Description:** Creates a new organization.

**Parameters:**
- Body:
  ```json
  {
    "name": "string"
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "organization": {
      "id": "string",
      "name": "string",
      "created_at": "timestamp",
      "created_by": "string"
    }
  }
}
```

**Error Responses:**
- `400 Bad Request`: If organization name is missing
- `401 Unauthorized`: If the user is not authenticated
- `500 Internal Server Error`: If there is a server error

### 4. Update Organization

**Endpoint:** `PUT /api/tenants/:id`

**Description:** Updates a specific organization's information.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the organization
- Body:
  ```json
  {
    "name": "string"
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "success": true
  }
}
```

**Error Responses:**
- `400 Bad Request`: If organization ID or name is missing, or if the update fails
- `401 Unauthorized`: If the user is not authenticated
- `500 Internal Server Error`: If there is a server error

### 5. Delete Organization

**Endpoint:** `DELETE /api/tenants/:id`

**Description:** Deletes a specific organization.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the organization

**Authorization:** Required

**Returns:**
```json
{
  "success": true
}
```

**Error Responses:**
- `400 Bad Request`: If organization ID is missing or if the deletion fails
- `401 Unauthorized`: If the user is not authenticated
- `500 Internal Server Error`: If there is a server error

### 6. Get Organization By Name

**Endpoint:** `GET /api/tenants/name/:name`

**Description:** Retrieves a specific organization by its name.

**Parameters:**
- Path Parameters:
  - `name` (required): Name of the organization

**Authorization:** Required

**Returns:**
```json
{
  "organization": {
    "id": "string",
    "name": "string",
    "created_at": "timestamp",
    "created_by": "string",
    "updated_at": "timestamp"
  }
}
```

**Error Responses:**
- `400 Bad Request`: If organization name is missing
- `401 Unauthorized`: If the user is not authenticated
- `404 Not Found`: If the organization is not found
- `500 Internal Server Error`: If there is a server error

### 7. Get Organizations By Creator

**Endpoint:** `GET /api/tenants/created-by/:createdBy`

**Description:** Retrieves organizations created by a specific user.

**Parameters:**
- Path Parameters:
  - `createdBy` (required): ID of the creator user

**Authorization:** Required

**Returns:**
```json
{
  "organizations": [
    {
      "id": "string",
      "name": "string",
      "created_at": "timestamp",
      "created_by": "string",
      "updated_at": "timestamp"
    }
  ]
}
```

**Error Responses:**
- `400 Bad Request`: If the creator ID is missing
- `401 Unauthorized`: If the user is not authenticated
- `500 Internal Server Error`: If there is a server error