# Role API Documentation

Base API path: `/api/roles`

This API provides endpoints for managing roles within the system. All endpoints require authorization.

## Endpoints

### Get All Roles

**GET** `/api/roles/`

Retrieve a list of all roles based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of roles to return
- `skip` (optional): Number of roles to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "success": true,
  "data": {
    "roles": [
      {
        "id": "string",
        "name": "string",
        "member_role_id": "string",
        "permission_id": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Authorization**
Needs policy for `RoleController.getRoles`

---

### Get Role by ID

**GET** `/api/roles/:id`

Retrieve a specific role by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the role

**Response**
```json
{
  "success": true,
  "data": {
    "role": {
      "id": "string",
      "name": "string",
      "member_role_id": "string",
      "permission_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When role ID is not provided
- `404 Not Found`: When the role with the provided ID doesn't exist

**Authorization**
Needs policy for `RoleController.getRoleById`

---

### Create Role

**POST** `/api/roles/`

Create a new role.

**Request Body**
```json
{
  "name": "string",  // required
  "member_role_id": "string",  // optional, ID of a member role to associate
  "permission_id": "string"  // optional, ID of a permission to associate
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "role": {
      "id": "string",
      "name": "string",
      "member_role_id": "string",
      "permission_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  },
  "message": "Role created successfully"
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing

**Authorization**
Needs policy for `RoleController.createRole`

---

### Update Role

**PUT** `/api/roles/:id`

Update an existing role.

**Path Parameters**
- `id` (required): The unique identifier of the role to update

**Request Body**
```json
{
  "name": "string",  // optional
  "member_role_id": "string",  // optional
  "permission_id": "string"  // optional
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Role updated successfully"
}
```

**Error Responses**
- `400 Bad Request`: When role ID is not provided or update fails

**Authorization**
Needs policy for `RoleController.updateRole`

---

### Delete Role

**DELETE** `/api/roles/:id`

Delete a role by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the role to delete

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Role deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When role ID is not provided or deletion fails

**Authorization**
Needs policy for `RoleController.deleteRole`

---

### Get Role by Name

**GET** `/api/roles/name/:name`

Retrieve a role by its name.

**Path Parameters**
- `name` (required): The name of the role

**Response**
```json
{
  "success": true,
  "data": {
    "role": {
      "id": "string",
      "name": "string",
      "member_role_id": "string",
      "permission_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When role name is not provided
- `404 Not Found`: When the role with the provided name doesn't exist

**Authorization**
Needs policy for `RoleController.getRoleByName`

---

### Get Roles by Member Role ID

**GET** `/api/roles/member-role/:memberRoleId`

Retrieve roles that are associated with a specific member role ID.

**Path Parameters**
- `memberRoleId` (required): The ID of the member role

**Response**
```json
{
  "success": true,
  "data": {
    "roles": [
      {
        "id": "string",
        "name": "string",
        "member_role_id": "string",
        "permission_id": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When member role ID is not provided

**Authorization**
Needs policy for `RoleController.getRolesByMemberRoleId`

---

### Get Roles by Permission ID

**GET** `/api/roles/permission/:permissionId`

Retrieve roles that are associated with a specific permission ID.

**Path Parameters**
- `permissionId` (required): The ID of the permission

**Response**
```json
{
  "success": true,
  "data": {
    "roles": [
      {
        "id": "string",
        "name": "string",
        "member_role_id": "string",
        "permission_id": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When permission ID is not provided

**Authorization**
Needs policy for `RoleController.getRolesByPermissionId`

---

### Get Roles by Creator

**GET** `/api/roles/created-by/:createdBy`

Retrieve roles that were created by a specific user.

**Path Parameters**
- `createdBy` (required): The ID of the user who created the roles

**Response**
```json
{
  "success": true,
  "data": {
    "roles": [
      {
        "id": "string",
        "name": "string",
        "member_role_id": "string",
        "permission_id": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When creator ID is not provided

**Authorization**
Needs policy for `RoleController.getRolesByCreator`
