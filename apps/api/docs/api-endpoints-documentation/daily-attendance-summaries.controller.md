# Daily Attendance Summaries API Documentation

Base API path: `/api/attendance-summaries`

This API provides endpoints for managing daily attendance summaries in the system. All endpoints require authorization.

## Endpoints

### Get All Attendance Summaries

**GET** `/api/attendance-summaries/`

Retrieve a list of all attendance summaries based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of summaries to return
- `skip` (optional): Number of summaries to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "summaries": [
    {
      "id": "string",
      "user_id": "string",
      "shift_id": "string",
      "work_date": "2023-01-01T00:00:00.000Z",
      "checkin_time": "2023-01-01T09:00:00.000Z",
      "checkout_time": "2023-01-01T17:00:00.000Z",
      "status": "string",
      "is_late": false,
      "is_early_leave": false,
      "holiday_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Needs policy for `DailyAttendanceSummariesController.getAttendanceSummaries`

---

### Get Late Attendance Summaries

**GET** `/api/attendance-summaries/late`

Retrieve all attendance summaries where the employee arrived late.

**Response**
```json
{
  "summaries": [
    {
      "id": "string",
      "user_id": "string",
      "shift_id": "string",
      "work_date": "2023-01-01T00:00:00.000Z",
      "checkin_time": "2023-01-01T09:15:00.000Z",
      "checkout_time": "2023-01-01T17:00:00.000Z",
      "status": "string",
      "is_late": true,
      "is_early_leave": false,
      "holiday_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Requires a valid authenticated user

---

### Get Early Leave Attendance Summaries

**GET** `/api/attendance-summaries/early-leave`

Retrieve all attendance summaries where the employee left early.

**Response**
```json
{
  "summaries": [
    {
      "id": "string",
      "user_id": "string",
      "shift_id": "string",
      "work_date": "2023-01-01T00:00:00.000Z",
      "checkin_time": "2023-01-01T09:00:00.000Z",
      "checkout_time": "2023-01-01T16:30:00.000Z",
      "status": "string",
      "is_late": false,
      "is_early_leave": true,
      "holiday_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Requires a valid authenticated user

---

### Get Attendance Summaries By User ID

**GET** `/api/attendance-summaries/user/:userId`

Retrieve all attendance summaries for a specific user.

**Path Parameters**
- `userId` (required): The ID of the user

**Response**
```json
{
  "summaries": [
    {
      "id": "string",
      "user_id": "string",
      "shift_id": "string",
      "work_date": "2023-01-01T00:00:00.000Z",
      "checkin_time": "2023-01-01T09:00:00.000Z",
      "checkout_time": "2023-01-01T17:00:00.000Z",
      "status": "string",
      "is_late": false,
      "is_early_leave": false,
      "holiday_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When user ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Requires a valid authenticated user

---

### Get Attendance Summaries By User ID and Date Range

**GET** `/api/attendance-summaries/user/:userId/date-range/:startDate/:endDate`

Retrieve attendance summaries for a specific user within a date range.

**Path Parameters**
- `userId` (required): The ID of the user
- `startDate` (required): The start date in ISO format (YYYY-MM-DD)
- `endDate` (required): The end date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "summaries": [
    {
      "id": "string",
      "user_id": "string",
      "shift_id": "string",
      "work_date": "2023-01-01T00:00:00.000Z",
      "checkin_time": "2023-01-01T09:00:00.000Z",
      "checkout_time": "2023-01-01T17:00:00.000Z",
      "status": "string",
      "is_late": false,
      "is_early_leave": false,
      "holiday_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When user ID, start date, or end date are not provided or have an invalid format
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Requires a valid authenticated user

---

### Get Attendance Summaries By Shift ID

**GET** `/api/attendance-summaries/shift/:shiftId`

Retrieve all attendance summaries for a specific shift.

**Path Parameters**
- `shiftId` (required): The ID of the shift

**Response**
```json
{
  "summaries": [
    {
      "id": "string",
      "user_id": "string",
      "shift_id": "string",
      "work_date": "2023-01-01T00:00:00.000Z",
      "checkin_time": "2023-01-01T09:00:00.000Z",
      "checkout_time": "2023-01-01T17:00:00.000Z",
      "status": "string",
      "is_late": false,
      "is_early_leave": false,
      "holiday_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When shift ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Requires a valid authenticated user

---

### Get Attendance Summaries By Holiday ID

**GET** `/api/attendance-summaries/holiday/:holidayId`

Retrieve all attendance summaries for a specific holiday.

**Path Parameters**
- `holidayId` (required): The ID of the holiday

**Response**
```json
{
  "summaries": [
    {
      "id": "string",
      "user_id": "string",
      "shift_id": "string",
      "work_date": "2023-01-01T00:00:00.000Z",
      "checkin_time": "2023-01-01T09:00:00.000Z",
      "checkout_time": "2023-01-01T17:00:00.000Z",
      "status": "string",
      "is_late": false,
      "is_early_leave": false,
      "holiday_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When holiday ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Requires a valid authenticated user

---

### Get Attendance Summaries By Work Date

**GET** `/api/attendance-summaries/work-date/:workDate`

Retrieve all attendance summaries for a specific work date.

**Path Parameters**
- `workDate` (required): The work date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "summaries": [
    {
      "id": "string",
      "user_id": "string",
      "shift_id": "string",
      "work_date": "2023-01-01T00:00:00.000Z",
      "checkin_time": "2023-01-01T09:00:00.000Z",
      "checkout_time": "2023-01-01T17:00:00.000Z",
      "status": "string",
      "is_late": false,
      "is_early_leave": false,
      "holiday_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When work date is not provided or has an invalid format
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Requires a valid authenticated user

---

### Get Attendance Summaries By Date Range

**GET** `/api/attendance-summaries/date-range/:startDate/:endDate`

Retrieve all attendance summaries within a date range.

**Path Parameters**
- `startDate` (required): The start date in ISO format (YYYY-MM-DD)
- `endDate` (required): The end date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "summaries": [
    {
      "id": "string",
      "user_id": "string",
      "shift_id": "string",
      "work_date": "2023-01-01T00:00:00.000Z",
      "checkin_time": "2023-01-01T09:00:00.000Z",
      "checkout_time": "2023-01-01T17:00:00.000Z",
      "status": "string",
      "is_late": false,
      "is_early_leave": false,
      "holiday_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When start date or end date are not provided or have an invalid format
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Requires a valid authenticated user

---

### Get Attendance Summary By ID

**GET** `/api/attendance-summaries/:id`

Retrieve a specific attendance summary by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the attendance summary

**Response**
```json
{
  "summary": {
    "id": "string",
    "user_id": "string",
    "shift_id": "string",
    "work_date": "2023-01-01T00:00:00.000Z",
    "checkin_time": "2023-01-01T09:00:00.000Z",
    "checkout_time": "2023-01-01T17:00:00.000Z",
    "status": "string",
    "is_late": false,
    "is_early_leave": false,
    "holiday_id": "string",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z",
    "created_by": "string"
  }
}
```

**Error Responses**
- `400 Bad Request`: When attendance summary ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the attendance summary with the provided ID doesn't exist
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Requires a valid authenticated user

---

### Create Attendance Summary

**POST** `/api/attendance-summaries/`

Create a new attendance summary.

**Request Body**
```json
{
  "user_id": "string",                      // required
  "shift_id": "string",                     // required
  "work_date": "2023-01-01T00:00:00.000Z",  // required
  "checkin_time": "2023-01-01T09:00:00.000Z",  // optional
  "checkout_time": "2023-01-01T17:00:00.000Z", // optional
  "status": "string",                      // optional
  "is_late": false,                        // optional
  "is_early_leave": false,                 // optional
  "holiday_id": "string"                   // optional
}
```

**Response**
```json
{
  "summary": {
    "id": "string",
    "user_id": "string",
    "shift_id": "string",
    "work_date": "2023-01-01T00:00:00.000Z",
    "checkin_time": "2023-01-01T09:00:00.000Z",
    "checkout_time": "2023-01-01T17:00:00.000Z",
    "status": "string",
    "is_late": false,
    "is_early_leave": false,
    "holiday_id": "string",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z",
    "created_by": "string"
  }
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Needs policy for `DailyAttendanceSummariesController.createAttendanceSummary`

---

### Update Attendance Summary

**PUT** `/api/attendance-summaries/:id`

Update an existing attendance summary.

**Path Parameters**
- `id` (required): The unique identifier of the attendance summary to update

**Request Body**
```json
{
  "user_id": "string",                      // optional
  "shift_id": "string",                     // optional
  "work_date": "2023-01-01T00:00:00.000Z",  // optional
  "checkin_time": "2023-01-01T09:00:00.000Z",  // optional
  "checkout_time": "2023-01-01T17:00:00.000Z", // optional
  "status": "string",                      // optional
  "is_late": false,                        // optional
  "is_early_leave": false,                 // optional
  "holiday_id": "string"                   // optional
}
```

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When attendance summary ID is not provided or update fails
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Needs policy for `DailyAttendanceSummariesController.updateAttendanceSummary`

---

### Delete Attendance Summary

**DELETE** `/api/attendance-summaries/:id`

Delete an attendance summary by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the attendance summary to delete

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When attendance summary ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When an unexpected error occurs

**Authorization**
Needs policy for `DailyAttendanceSummariesController.deleteAttendanceSummary`