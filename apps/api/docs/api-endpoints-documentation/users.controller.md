# Users Controller API Documentation

Base path: `/api/users`

## Endpoints

### 1. Get All Users

**Endpoint:** `GET /api/users/`

**Description:** Retrieves a list of all users with optional pagination and sorting.

**Parameters:**
- Query Parameters:
  - `limit` (optional): Number of users to retrieve
  - `skip` (optional): Number of users to skip
  - `sortBy` (optional): Field to sort by
  - `sortDirection` (optional): Sort direction (asc/desc)

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "string",
        "username": "string",
        "name": "string",
        "email": "string",
        "status": "active|inactive|suspended|pending",
        "code": "string",
        "unit_id": "string",
        "created_at": "timestamp",
        "updated_at": "timestamp"
      }
    ]
  }
}
```

### 2. Get Users By Unit ID

**Endpoint:** `GET /api/users/unit/:unitId`

**Description:** Finds users belonging to a specific unit with pagination.

**Parameters:**
- Path Parameters:
  - `unitId` (required): ID of the unit
- Query Parameters:
  - `pageSize` (optional): Number of users per page (default: 10)
  - `pageIndex` (optional): Page number (default: 0)

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "string",
        "username": "string",
        "name": "string",
        "email": "string",
        "status": "string",
        "unit_id": "string"
      }
    ],
    "total": "number",
    "pageSize": "number",
    "pageIndex": "number"
  }
}
```

### 3. Get Users By Member Role ID

**Endpoint:** `GET /api/users/role/:memberRoleId`

**Description:** Finds users with a specific member role.

**Parameters:**
- Path Parameters:
  - `memberRoleId` (required): ID of the member role

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "string",
        "username": "string",
        "name": "string",
        "email": "string",
        "status": "string"
      }
    ]
  }
}
```

### 4. Get Users By Tenant ID

**Endpoint:** `GET /api/users/tenant/:tenantId`

**Description:** Finds users belonging to a specific tenant with pagination.

**Parameters:**
- Path Parameters:
  - `tenantId` (required): ID of the tenant
- Query Parameters:
  - `pageSize` (optional): Number of users per page (default: 10)
  - `pageIndex` (optional): Page number (default: 0)

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "string",
        "username": "string",
        "name": "string",
        "email": "string",
        "status": "string",
        "tenant_id": "string"
      }
    ],
    "total": "number",
    "pageSize": "number",
    "pageIndex": "number"
  }
}
```

### 5. Authenticate User

**Endpoint:** `POST /api/users/authenticate`

**Description:** Authenticates a user using username and password.

**Parameters:**
- Body:
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "name": "string",
      "email": "string",
      "status": "string"
    },
    "token": "string"
  }
}
```

### 6. Get User By ID

**Endpoint:** `GET /api/users/:id`

**Description:** Retrieves a specific user by their ID.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the user

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "name": "string",
      "email": "string",
      "status": "string",
      "code": "string",
      "unit_id": "string",
      "created_at": "timestamp",
      "updated_at": "timestamp"
    }
  }
}
```

### 7. Create User

**Endpoint:** `POST /api/users/`

**Description:** Creates a new user.

**Parameters:**
- Body:
  ```json
  {
    "username": "string",
    "password": "string",
    "name": "string",
    "email": "string",
    "unit_id": "string",
    "code": "string" // Optional, auto-generated if not provided
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "name": "string",
      "email": "string",
      "status": "string",
      "code": "string",
      "unit_id": "string",
      "created_at": "timestamp",
      "created_by": "string"
    }
  },
  "message": "User created successfully"
}
```

### 8. Update Users Status

**Endpoint:** `PUT /api/users/bulk-update-status`

**Description:** Updates the status of multiple users at once.

**Parameters:**
- Body:
  ```json
  {
    "userIds": ["string", "string"],
    "status": "active|inactive|suspended|pending"
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "updatedCount": "number",
    "updatedUsers": ["string"]
  },
  "message": "Successfully updated status for X user(s)"
}
```

### 9. Upload User Avatar

**Endpoint:** `POST /api/users/:id/avatar`

**Description:** Uploads an avatar for a specific user.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the user
- Body:
  ```json
  {
    "fileName": "string",
    "fileData": "string" // Base64 encoded image data
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "file_id": "string",
    "avatar_url": "/api/storage/files/{fileId}/stream"
  },
  "message": "Avatar uploaded successfully"
}
```

### 10. Update User

**Endpoint:** `PUT /api/users/:id`

**Description:** Updates a specific user's information.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the user
- Body: User data to update (fields that can be updated)
  ```json
  {
    "username": "string",
    "name": "string",
    "email": "string",
    "unit_id": "string",
    "status": "string",
    "code": "string"
    // Note: Password updates may be handled differently
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "User updated successfully"
}
```

### 11. Delete User

**Endpoint:** `DELETE /api/users/:id`

**Description:** Deletes a specific user.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the user

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "User deleted successfully"
}
```