# Edge Device API Documentation

Base API path: `/api/edge-devices`

This API provides endpoints for managing edge devices within the system. All endpoints require authorization.

## Endpoints

### Get All Edge Devices

**GET** `/api/edge-devices/`

Retrieve a list of all edge devices based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of devices to return
- `skip` (optional): Number of devices to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "success": true,
  "data": {
    "devices": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "type": "string",
        "status": "string",
        "camera_id": "string",
        "ip_address": "string",
        "mac_address": "string",
        "firmware_version": "string",
        "location": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Authorization**
Needs policy for `EdgeDeviceController.getEdgeDevices`

---

### Get Attendance Devices

**GET** `/api/edge-devices/attendance`

Retrieve a list of edge devices specifically used for attendance.

**Response**
```json
{
  "success": true,
  "data": {
    "devices": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "type": "attendance",
        "status": "string",
        "camera_id": "string",
        "ip_address": "string",
        "mac_address": "string",
        "firmware_version": "string",
        "location": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Authorization**
Requires a valid authenticated user

---

### Get Edge Devices by Camera ID

**GET** `/api/edge-devices/camera/:cameraId`

Retrieve edge devices associated with a specific camera.

**Path Parameters**
- `cameraId` (required): The ID of the camera

**Response**
```json
{
  "success": true,
  "data": {
    "devices": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "type": "string",
        "status": "string",
        "camera_id": "string",
        "ip_address": "string",
        "mac_address": "string",
        "firmware_version": "string",
        "location": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When camera ID is not provided

**Authorization**
Requires a valid authenticated user

---

### Get Edge Devices by Type

**GET** `/api/edge-devices/type/:type`

Retrieve edge devices of a specific type.

**Path Parameters**
- `type` (required): The type of edge device (e.g., "attendance", "surveillance")

**Response**
```json
{
  "success": true,
  "data": {
    "devices": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "type": "string",
        "status": "string",
        "camera_id": "string",
        "ip_address": "string",
        "mac_address": "string",
        "firmware_version": "string",
        "location": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When device type is not provided

**Authorization**
Requires a valid authenticated user

---

### Get Edge Devices by Status

**GET** `/api/edge-devices/status/:status`

Retrieve edge devices with a specific status.

**Path Parameters**
- `status` (required): The status of edge device (e.g., "active", "inactive", "maintenance")

**Response**
```json
{
  "success": true,
  "data": {
    "devices": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "type": "string",
        "status": "string",
        "camera_id": "string",
        "ip_address": "string",
        "mac_address": "string",
        "firmware_version": "string",
        "location": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When device status is not provided

**Authorization**
Requires a valid authenticated user

---

### Get Edge Device by IP Address

**GET** `/api/edge-devices/ip/:ipAddress`

Retrieve an edge device by its IP address.

**Path Parameters**
- `ipAddress` (required): The IP address of the edge device

**Response**
```json
{
  "success": true,
  "data": {
    "device": {
      "id": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "status": "string",
      "camera_id": "string",
      "ip_address": "string",
      "mac_address": "string",
      "firmware_version": "string",
      "location": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When IP address is not provided
- `404 Not Found`: When no edge device is found with the provided IP address

**Authorization**
Requires a valid authenticated user

---

### Get Edge Device by MAC Address

**GET** `/api/edge-devices/mac/:macAddress`

Retrieve an edge device by its MAC address.

**Path Parameters**
- `macAddress` (required): The MAC address of the edge device

**Response**
```json
{
  "success": true,
  "data": {
    "device": {
      "id": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "status": "string",
      "camera_id": "string",
      "ip_address": "string",
      "mac_address": "string",
      "firmware_version": "string",
      "location": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When MAC address is not provided
- `404 Not Found`: When no edge device is found with the provided MAC address

**Authorization**
Requires a valid authenticated user

---

### Get Edge Devices by Firmware Version

**GET** `/api/edge-devices/firmware/:version`

Retrieve edge devices with a specific firmware version.

**Path Parameters**
- `version` (required): The firmware version

**Response**
```json
{
  "success": true,
  "data": {
    "devices": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "type": "string",
        "status": "string",
        "camera_id": "string",
        "ip_address": "string",
        "mac_address": "string",
        "firmware_version": "string",
        "location": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When firmware version is not provided

**Authorization**
Requires a valid authenticated user

---

### Get Edge Device by ID

**GET** `/api/edge-devices/:id`

Retrieve a specific edge device by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the edge device

**Response**
```json
{
  "success": true,
  "data": {
    "device": {
      "id": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "status": "string",
      "camera_id": "string",
      "ip_address": "string",
      "mac_address": "string",
      "firmware_version": "string",
      "location": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When device ID is not provided
- `404 Not Found`: When the device with the provided ID doesn't exist

**Authorization**
Needs policy for `EdgeDeviceController.getEdgeDeviceById`

---

### Create Edge Device

**POST** `/api/edge-devices/`

Create a new edge device.

**Request Body**
```json
{
  "name": "string",               // required
  "description": "string",        // optional
  "type": "string",               // required (e.g., "attendance", "surveillance")
  "status": "string",             // required (e.g., "active", "inactive")
  "camera_id": "string",          // optional
  "ip_address": "string",         // optional
  "mac_address": "string",        // optional
  "firmware_version": "string",   // optional
  "location": "string"            // optional
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "device": {
      "id": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "status": "string",
      "camera_id": "string",
      "ip_address": "string",
      "mac_address": "string",
      "firmware_version": "string",
      "location": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  },
  "message": "Edge device created successfully"
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceController.createEdgeDevice`

---

### Update Edge Device

**PUT** `/api/edge-devices/:id`

Update an existing edge device.

**Path Parameters**
- `id` (required): The unique identifier of the edge device to update

**Request Body**
```json
{
  "name": "string",               // optional
  "description": "string",        // optional
  "type": "string",               // optional
  "status": "string",             // optional
  "camera_id": "string",          // optional
  "ip_address": "string",         // optional
  "mac_address": "string",        // optional
  "firmware_version": "string",   // optional
  "location": "string"            // optional
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Edge device updated successfully"
}
```

**Error Responses**
- `400 Bad Request`: When device ID is not provided or update fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceController.updateEdgeDevice`

---

### Delete Edge Device

**DELETE** `/api/edge-devices/:id`

Delete an edge device by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the edge device to delete

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Edge device deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When device ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceController.deleteEdgeDevice`