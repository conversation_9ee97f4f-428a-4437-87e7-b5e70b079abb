# Permission API Documentation

Base API path: `/api/permissions`

This API provides endpoints for managing permissions in the system. All endpoints require authorization.

## Endpoints

### Get All Permissions

**GET** `/api/permissions/`

Retrieve a list of all permissions based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of permissions to return
- `skip` (optional): Number of permissions to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "success": true,
  "data": {
    "permissions": [
      {
        "id": "string",
        "role_id": "string",
        "module": "string",
        "feature": "string",
        "action": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `PermissionController.getPermissions`

---

### Get Permission By ID

**GET** `/api/permissions/:id`

Retrieve a specific permission by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the permission

**Response**
```json
{
  "success": true,
  "data": {
    "permission": {
      "id": "string",
      "role_id": "string",
      "module": "string",
      "feature": "string",
      "action": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When permission ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the permission with the provided ID doesn't exist

**Authorization**
Needs policy for `PermissionController.getPermissionById`

---

### Create Permission

**POST** `/api/permissions/`

Create a new permission.

**Request Body**
```json
{
  "roleId": "string",  // required - Role ID to assign the permission to
  "module": "string",  // required - Module name (e.g., "users", "cameras")
  "feature": "string", // required - Feature within the module
  "action": "string"   // required - Action (e.g., "read", "write", "delete")
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "permission": {
      "id": "string",
      "role_id": "string",
      "module": "string",
      "feature": "string",
      "action": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  },
  "message": "Permission created successfully"
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `PermissionController.createPermission`

---

### Delete Permission

**DELETE** `/api/permissions/:id`

Delete a permission by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the permission to delete

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Permission deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When permission ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `PermissionController.deletePermission`

---

### Check Permission

**GET** `/api/permissions/check/:roleId/:module/:feature/:action`

Check if a role has a specific permission.

**Path Parameters**
- `roleId` (required): The ID of the role
- `module` (required): The module name
- `feature` (required): The feature name
- `action` (required): The action name

**Response**
```json
{
  "success": true,
  "data": {
    "hasPermission": true
  }
}
```

**Error Responses**
- `400 Bad Request`: When any of the required parameters are missing
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `PermissionController.checkPermission`

---

### Get Permissions For Role

**GET** `/api/permissions/role/:roleId`

Get all permissions assigned to a specific role.

**Path Parameters**
- `roleId` (required): The ID of the role

**Response**
```json
{
  "success": true,
  "data": {
    "permissions": [
      {
        "id": "string",
        "role_id": "string",
        "module": "string",
        "feature": "string",
        "action": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When role ID is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `PermissionController.getPermissionsForRole`

---

### Get Permissions For Module

**GET** `/api/permissions/module/:module`

Get all permissions related to a specific module.

**Path Parameters**
- `module` (required): The name of the module

**Response**
```json
{
  "success": true,
  "data": {
    "permissions": [
      {
        "id": "string",
        "role_id": "string",
        "module": "string",
        "feature": "string",
        "action": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When module name is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `PermissionController.getPermissionsForModule`

---

### Get Permissions For Feature

**GET** `/api/permissions/feature/:feature`

Get all permissions related to a specific feature.

**Path Parameters**
- `feature` (required): The name of the feature

**Response**
```json
{
  "success": true,
  "data": {
    "permissions": [
      {
        "id": "string",
        "role_id": "string",
        "module": "string",
        "feature": "string",
        "action": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When feature name is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `PermissionController.getPermissionsForFeature`

---

### Get Permissions For Action

**GET** `/api/permissions/action/:action`

Get all permissions related to a specific action.

**Path Parameters**
- `action` (required): The name of the action

**Response**
```json
{
  "success": true,
  "data": {
    "permissions": [
      {
        "id": "string",
        "role_id": "string",
        "module": "string",
        "feature": "string",
        "action": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When action name is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `PermissionController.getPermissionsForAction`

---

### Remove All Permissions For Role

**DELETE** `/api/permissions/role/:roleId`

Remove all permissions assigned to a specific role.

**Path Parameters**
- `roleId` (required): The ID of the role

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "All permissions removed for role successfully"
}
```

**Error Responses**
- `400 Bad Request`: When role ID is not provided or removal fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `PermissionController.removeAllPermissionsForRole`