# Identity API Documentation

Base API path: `/api/identity`

This API provides endpoints for handling identity-related operations including authentication, token management, and user profile operations.

## Endpoints

### Login

**POST** `/api/identity/login`

Authenticate a user and return tokens.

**Request Body**
```json
{
  "username": "string",  // required
  "password": "string",  // required
  "deviceInfo": {        // optional
    "deviceName": "string",
    "deviceType": "string",
    "osName": "string",
    "osVersion": "string"
  }
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "name": "string",
      "status": "active",
      "code": "string",
      "unit_id": "string",
      "member_role_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    },
    "token": "string",
    "expires_in": 3600,
    "token_type": "Bearer",
    "roles": ["string"],
    "permissions": ["string"]
  }
}
```

**Notes**
- The refresh token is set as an HTTP-only secure cookie
- Returns user profile information and access token

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When credentials are invalid

**Authorization**
No authorization required (AllowAnonymous)

---

### Refresh Token

**POST** `/api/identity/refresh-token`

Refresh the access token using the refresh token stored in cookies.

**Response**
```json
{
  "success": true,
  "data": {
    "token": "string",
    "expires_in": 3600,
    "token_type": "Bearer"
  }
}
```

**Notes**
- The refresh token must be provided via cookies
- The refresh token is rotated and a new one is set as a cookie

**Error Responses**
- `401 Unauthorized`: When the refresh token is invalid, expired, or has been revoked

**Authorization**
No authorization required (AllowAnonymous)

---

### Logout

**POST** `/api/identity/logout`

Log out the current user by revoking their tokens.

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Successfully logged out"
}
```

**Notes**
- Deletes the refresh token cookie
- Revokes the access token and refresh token in the database

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Requires a valid authenticated user

---

### Logout All Sessions

**POST** `/api/identity/logout-all`

Log out from all sessions across all devices.

**Response**
```json
{
  "success": true,
  "data": {
    "success": true,
    "revoked_count": 10
  },
  "message": "Successfully logged out from all sessions"
}
```

**Notes**
- Revokes all tokens (both access and refresh) for the current user
- Returns the count of tokens revoked

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Requires a valid authenticated user

---

### Get Current User Profile

**GET** `/api/identity/me`

Get the current user's profile information.

**Response**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "name": "string",
      "status": "active",
      "code": "string",
      "unit_id": "string",
      "member_role_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    },
    "roles": ["string"],
    "permissions": ["string"]
  }
}
```

**Notes**
- Returns the user's profile, roles, and permissions

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the user record is not found in the database

**Authorization**
Requires a valid authenticated user

---

### Update Profile

**PUT** `/api/identity/profile`

Update the current user's profile information.

**Request Body**
```json
{
  "name": "string",  // optional
  "code": "string"   // optional
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "name": "string",
      "status": "active",
      "code": "string",
      "unit_id": "string",
      "member_role_id": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  },
  "message": "Profile updated successfully"
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the user record is not found in the database
- `400 Bad Request`: When the update fails

**Authorization**
Requires a valid authenticated user

---

### Get Active Sessions

**GET** `/api/identity/sessions`

Get a list of all active sessions for the current user.

**Response**
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "id": "string",
        "device_info": {
          "deviceName": "string",
          "deviceType": "string",
          "osName": "string",
          "osVersion": "string"
        },
        "ip_address": "string",
        "user_agent": "string",
        "last_active": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "is_current": true
      }
    ]
  }
}
```

**Notes**
- The current session is marked with `is_current: true`

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Requires a valid authenticated user

---

### Revoke Session

**POST** `/api/identity/sessions/:sessionId/revoke`

Revoke a specific session by its ID.

**Path Parameters**
- `sessionId` (required): The unique identifier of the session to revoke

**Response**
```json
{
  "success": true,
  "data": {
    "success": true,
    "session_id": "string",
    "revoked_tokens": 2
  }
}
```

**Notes**
- Cannot revoke the current session; use logout instead
- Returns the count of tokens revoked

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated or attempting to revoke current session
- `404 Not Found`: When the session is not found or doesn't belong to the user

**Authorization**
Requires a valid authenticated user

---

### Verify Token

**POST** `/api/identity/verify-token`

Verify if a token is valid and return its payload.

**Request Body**
```json
{
  "token": "string"  // required
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "payload": {
      "user_id": "string",
      "username": "string",
      "role": "string",
      "unit_id": "string",
      "member_role_id": "string",
      "roles": ["string"],
      "permissions": ["string"],
      "expires_at": "2023-01-01T00:00:00.000Z"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When token is not provided
- `401 Unauthorized`: When the token is invalid or expired

**Authorization**
No authorization required (AllowAnonymous)