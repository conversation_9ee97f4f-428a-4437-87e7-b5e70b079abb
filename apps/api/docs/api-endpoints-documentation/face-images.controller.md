# Face Images API Documentation

Base API path: `/api/face-images`

This API provides endpoints for managing face images in the system. Most endpoints require authorization.

## Endpoints

### Get All Face Images

**GET** `/api/face-images/`

Retrieve a list of all face images based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of images to return
- `skip` (optional): Number of images to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "success": true,
  "data": {
    "faceImages": [
      {
        "id": "string",
        "user_id": "string",
        "image_url": "string",
        "image_angle": "string",
        "embedding": "string",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Authorization**
Needs policy for `FaceImagesController.getFaceImages`

---

### Get Face Image by ID

**GET** `/api/face-images/:id`

Retrieve a specific face image by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the face image

**Response**
```json
{
  "success": true,
  "data": {
    "faceImage": {
      "id": "string",
      "user_id": "string",
      "image_url": "string",
      "image_angle": "string",
      "embedding": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When face image ID is not provided
- `404 Not Found`: When the face image with the provided ID doesn't exist

**Authorization**
Needs policy for `FaceImagesController.getFaceImageById`

---

### Create Face Image

**POST** `/api/face-images/`

Create a new face image record.

**Request Body**
```json
{
  "user_id": "string",     // required
  "image_url": "string",   // required
  "image_angle": "string", // optional
  "embedding": "string"    // optional
}
```

**Response**
```json
{
  "faceImage": {
    "id": "string",
    "user_id": "string",
    "image_url": "string",
    "image_angle": "string",
    "embedding": "string",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z",
    "created_by": "string"
  }
}
```

**Error Responses**
- `400 Bad Request`: When required fields (user_id, image_url) are missing
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When the operation fails

**Authorization**
Needs policy for `FaceImagesController.createFaceImage`

---

### Update Face Image

**PUT** `/api/face-images/:id`

Update an existing face image record.

**Path Parameters**
- `id` (required): The unique identifier of the face image to update

**Request Body**
```json
{
  "user_id": "string",     // optional
  "image_url": "string",   // optional
  "image_angle": "string", // optional
  "embedding": "string"    // optional
}
```

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When face image ID is not provided or update fails
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When the operation fails

**Authorization**
Needs policy for `FaceImagesController.updateFaceImage`

---

### Delete Face Image

**DELETE** `/api/face-images/:id`

Delete a face image by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the face image to delete

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When face image ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When the operation fails

**Authorization**
Needs policy for `FaceImagesController.deleteFaceImage`

---

### Get Face Images by User ID

**GET** `/api/face-images/user/:userId`

Retrieve all face images belonging to a specific user.

**Path Parameters**
- `userId` (required): The ID of the user

**Response**
```json
{
  "faceImages": [
    {
      "id": "string",
      "user_id": "string",
      "image_url": "string",
      "image_angle": "string",
      "embedding": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When user ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When the operation fails

**Authorization**
Requires a valid authenticated user

---

### Get Face Images by Image Angle

**GET** `/api/face-images/angle/:imageAngle`

Retrieve all face images with a specific angle.

**Path Parameters**
- `imageAngle` (required): The angle of the face image

**Response**
```json
{
  "faceImages": [
    {
      "id": "string",
      "user_id": "string",
      "image_url": "string",
      "image_angle": "string",
      "embedding": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When image angle is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When the operation fails

**Authorization**
Requires a valid authenticated user

---

### Get Face Images by Date Range

**GET** `/api/face-images/date-range/:startDate/:endDate`

Retrieve all face images created within a specific date range.

**Path Parameters**
- `startDate` (required): The start date in ISO format (YYYY-MM-DD)
- `endDate` (required): The end date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "faceImages": [
    {
      "id": "string",
      "user_id": "string",
      "image_url": "string",
      "image_angle": "string",
      "embedding": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When start date or end date are not provided or have invalid format
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When the operation fails

**Authorization**
Requires a valid authenticated user

---

### Get Face Images by User ID and Image Angle

**GET** `/api/face-images/user/:userId/angle/:imageAngle`

Retrieve all face images for a specific user with a specific angle.

**Path Parameters**
- `userId` (required): The ID of the user
- `imageAngle` (required): The angle of the face image

**Response**
```json
{
  "faceImages": [
    {
      "id": "string",
      "user_id": "string",
      "image_url": "string",
      "image_angle": "string",
      "embedding": "string",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When user ID or image angle are not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When the operation fails

**Authorization**
Requires a valid authenticated user

---

### Delete Face Images by User ID

**DELETE** `/api/face-images/user/:userId`

Delete all face images belonging to a specific user.

**Path Parameters**
- `userId` (required): The ID of the user

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When user ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When the operation fails

**Authorization**
Requires a valid authenticated user

---

### Get Latest Front-facing Image

**GET** `/api/face-images/user/:userId/latest-front`

Retrieve the most recent front-facing face image for a specific user.

**Path Parameters**
- `userId` (required): The ID of the user

**Response**
```json
{
  "faceImage": {
    "id": "string",
    "user_id": "string",
    "image_url": "string",
    "image_angle": "front",
    "embedding": "string",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z",
    "created_by": "string"
  }
}
```

**Error Responses**
- `400 Bad Request`: When user ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When no front-facing image exists for the user
- `500 Internal Server Error`: When the operation fails

**Authorization**
Requires a valid authenticated user