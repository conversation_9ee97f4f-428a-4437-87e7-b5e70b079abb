# Shift API Documentation

Base API path: `/api/shifts`

This API provides endpoints for managing shifts in the system. All endpoints require authorization.

## Endpoints

### Get All Shifts

**GET** `/api/shifts/`

Retrieve a list of all shifts based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of shifts to return
- `skip` (optional): Number of shifts to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "shifts": [
    {
      "id": "string",
      "name": "Morning Shift",
      "shift_type": "regular",
      "work_coefficient": 1.0,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `ShiftController.getShifts`

---

### Get Shift By ID

**GET** `/api/shifts/:id`

Retrieve a specific shift by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the shift

**Response**
```json
{
  "shift": {
    "id": "string",
    "name": "Morning Shift",
    "shift_type": "regular",
    "work_coefficient": 1.0,
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
}
```

**Error Responses**
- `400 Bad Request`: When shift ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the shift with the provided ID doesn't exist

**Authorization**
Needs policy for `ShiftController.getShiftById`

---

### Create Shift

**POST** `/api/shifts/`

Create a new shift.

**Request Body**
```json
{
  "name": "Morning Shift",     // required - The name of the shift
  "shift_type": "regular",    // required - Type of shift (e.g., regular, night, weekend)
  "work_coefficient": 1.0      // required - Coefficient for work calculations
}
```

**Response**
```json
{
  "shift": {
    "id": "string",
    "name": "Morning Shift",
    "shift_type": "regular",
    "work_coefficient": 1.0,
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  },
  "message": "Shift created successfully"
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `ShiftController.createShift`

---

### Update Shift

**PUT** `/api/shifts/:id`

Update an existing shift.

**Path Parameters**
- `id` (required): The unique identifier of the shift to update

**Request Body**
```json
{
  "name": "Updated Morning Shift",     // optional - The name of the shift
  "shift_type": "regular",            // optional - Type of shift (e.g., regular, night, weekend)
  "work_coefficient": 1.2              // optional - Coefficient for work calculations
}
```

**Response**
```json
{
  "success": true,
  "message": "Shift updated successfully"
}
```

**Error Responses**
- `400 Bad Request`: When shift ID is not provided or update fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `ShiftController.updateShift`

---

### Delete Shift

**DELETE** `/api/shifts/:id`

Delete a shift by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the shift to delete

**Response**
```json
{
  "success": true,
  "message": "Shift deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When shift ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `ShiftController.deleteShift`

---

### Get Shift By Name

**GET** `/api/shifts/name/:name`

Retrieve a specific shift by its name.

**Path Parameters**
- `name` (required): The name of the shift

**Response**
```json
{
  "shift": {
    "id": "string",
    "name": "Morning Shift",
    "shift_type": "regular",
    "work_coefficient": 1.0,
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
}
```

**Error Responses**
- `400 Bad Request`: When shift name is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When no shift with the provided name exists

**Authorization**
Requires a valid authenticated user

---

### Get Shifts By Type

**GET** `/api/shifts/type/:shiftType`

Retrieve all shifts of a specific type.

**Path Parameters**
- `shiftType` (required): The type of shift to filter by (e.g., regular, night, weekend)

**Response**
```json
{
  "shifts": [
    {
      "id": "string",
      "name": "Morning Shift",
      "shift_type": "regular",
      "work_coefficient": 1.0,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When shift type is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Requires a valid authenticated user

---

### Get Shifts By Work Coefficient

**GET** `/api/shifts/coefficient/:workCoefficient`

Retrieve all shifts with a specific work coefficient.

**Path Parameters**
- `workCoefficient` (required): The work coefficient to filter by

**Response**
```json
{
  "shifts": [
    {
      "id": "string",
      "name": "Night Shift",
      "shift_type": "night",
      "work_coefficient": 1.5,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When work coefficient is not provided or invalid
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Requires a valid authenticated user