# Edge Device Logs API Documentation

Base API path: `/api/edge-device-logs`

This API provides endpoints for managing edge device logs in the system. All endpoints require authorization.

## Endpoints

### Get All Edge Device Logs

**GET** `/api/edge-device-logs/`

Retrieve a list of all edge device logs based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of logs to return
- `skip` (optional): Number of logs to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "string",
        "edge_device_id": "string",
        "log_level": "string",
        "message": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceLogsController.getEdgeDeviceLogs`

---

### Get Edge Device Log By ID

**GET** `/api/edge-device-logs/:id`

Retrieve a specific edge device log by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the edge device log

**Response**
```json
{
  "success": true,
  "data": {
    "log": {
      "id": "string",
      "edge_device_id": "string",
      "log_level": "string",
      "message": "string",
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  }
}
```

**Error Responses**
- `400 Bad Request`: When edge device log ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the edge device log with the provided ID doesn't exist

**Authorization**
Needs policy for `EdgeDeviceLogsController.getEdgeDeviceLogById`

---

### Create Edge Device Log

**POST** `/api/edge-device-logs/`

Create a new edge device log entry.

**Request Body**
```json
{
  "edge_device_id": "string",  // required
  "log_level": "string",      // required (e.g., "info", "warning", "error", "debug")
  "message": "string",        // required
  "timestamp": "2023-01-01T00:00:00.000Z" // optional
}
```

**Response**
```json
{
  "success": true,
  "data": {
    "log": {
      "id": "string",
      "edge_device_id": "string",
      "log_level": "string",
      "message": "string",
      "timestamp": "2023-01-01T00:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z",
      "created_by": "string"
    }
  },
  "message": "Edge device log created successfully"
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceLogsController.createEdgeDeviceLog`

---

### Delete Edge Device Log

**DELETE** `/api/edge-device-logs/:id`

Delete an edge device log by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the edge device log to delete

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Edge device log deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When edge device log ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the edge device log with the provided ID doesn't exist

**Authorization**
Needs policy for `EdgeDeviceLogsController.deleteEdgeDeviceLog`

---

### Get Logs By Edge Device ID

**GET** `/api/edge-device-logs/device/:edgeDeviceId`

Retrieve all logs for a specific edge device.

**Path Parameters**
- `edgeDeviceId` (required): The ID of the edge device

**Response**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "string",
        "edge_device_id": "string",
        "log_level": "string",
        "message": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When edge device ID is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceLogsController.getLogsByEdgeDeviceId`

---

### Get Logs By Log Level

**GET** `/api/edge-device-logs/level/:logLevel`

Retrieve all logs with a specific log level.

**Path Parameters**
- `logLevel` (required): The log level to filter by (e.g., "info", "warning", "error", "debug")

**Response**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "string",
        "edge_device_id": "string",
        "log_level": "string",
        "message": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When log level is not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceLogsController.getLogsByLogLevel`

---

### Get Logs By Date Range

**GET** `/api/edge-device-logs/date-range/:startDate/:endDate`

Retrieve all logs within a specific date range.

**Path Parameters**
- `startDate` (required): The start date in ISO format (YYYY-MM-DD)
- `endDate` (required): The end date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "string",
        "edge_device_id": "string",
        "log_level": "string",
        "message": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When start date or end date are not provided or have an invalid format
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Requires a valid authenticated user

---

### Get Logs By Edge Device ID and Date Range

**GET** `/api/edge-device-logs/device/:edgeDeviceId/date-range/:startDate/:endDate`

Retrieve all logs for a specific edge device within a date range.

**Path Parameters**
- `edgeDeviceId` (required): The ID of the edge device
- `startDate` (required): The start date in ISO format (YYYY-MM-DD)
- `endDate` (required): The end date in ISO format (YYYY-MM-DD)

**Response**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "string",
        "edge_device_id": "string",
        "log_level": "string",
        "message": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When edge device ID, start date, or end date are not provided or dates have an invalid format
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceLogsController.getLogsByEdgeDeviceIdAndDateRange`

---

### Get Logs By Edge Device ID and Log Level

**GET** `/api/edge-device-logs/device/:edgeDeviceId/level/:logLevel`

Retrieve all logs for a specific edge device with a specific log level.

**Path Parameters**
- `edgeDeviceId` (required): The ID of the edge device
- `logLevel` (required): The log level to filter by (e.g., "info", "warning", "error", "debug")

**Response**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "string",
        "edge_device_id": "string",
        "log_level": "string",
        "message": "string",
        "timestamp": "2023-01-01T00:00:00.000Z",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "created_by": "string"
      }
    ]
  }
}
```

**Error Responses**
- `400 Bad Request`: When edge device ID or log level are not provided
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceLogsController.getLogsByEdgeDeviceIdAndLogLevel`

---

### Delete Logs By Edge Device ID

**DELETE** `/api/edge-device-logs/device/:edgeDeviceId`

Delete all logs for a specific edge device.

**Path Parameters**
- `edgeDeviceId` (required): The ID of the edge device

**Response**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Edge device logs deleted successfully"
}
```

**Error Responses**
- `400 Bad Request`: When edge device ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated

**Authorization**
Needs policy for `EdgeDeviceLogsController.deleteLogsByEdgeDeviceId`